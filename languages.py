# -*- coding: utf-8 -*-
"""
多语言支持配置文件
支持中文和英文切换
"""

# 语言配置
LANGUAGES = {
    'zh': {
        'name': '中文',
        'code': 'zh',
        'flag': '🇨🇳'
    },
    'en': {
        'name': 'English',
        'code': 'en', 
        'flag': '🇺🇸'
    }
}

# 界面文本翻译
TRANSLATIONS = {
    # 网站标题和描述
    'site_title': {
        'zh': '🎮 热门游戏评分网站',
        'en': '🎮 Popular Game Rating Website'
    },
    'site_subtitle': {
        'zh': '发现最受欢迎的游戏，分享你的游戏体验',
        'en': 'Discover the most popular games and share your gaming experience'
    },
    
    # 导航和按钮
    'language_switch': {
        'zh': '语言',
        'en': 'Language'
    },
    'view_details': {
        'zh': '查看详情',
        'en': 'View Details'
    },
    'submit_rating': {
        'zh': '提交评分',
        'en': 'Submit Rating'
    },
    'submit_comment': {
        'zh': '发表评论',
        'en': 'Post Comment'
    },
    'back_to_home': {
        'zh': '返回首页',
        'en': 'Back to Home'
    },
    
    # 网站介绍部分
    'about_title': {
        'zh': '🎮 关于我们的游戏评分网站',
        'en': '🎮 About Our Game Rating Website'
    },
    'about_content_1': {
        'zh': '欢迎来到最专业的游戏评分平台！我们致力于为游戏爱好者提供一个公正、客观的游戏评价和交流社区。',
        'en': 'Welcome to the most professional game rating platform! We are committed to providing game enthusiasts with a fair and objective game evaluation and communication community.'
    },
    'about_content_2': {
        'zh': '在这里，您可以发现最新最热门的游戏，查看详细的游戏信息，阅读其他玩家的真实评价，并分享您自己的游戏体验。我们相信每一个玩家的声音都很重要，每一份评价都能帮助其他人做出更好的游戏选择。',
        'en': 'Here, you can discover the latest and hottest games, view detailed game information, read real reviews from other players, and share your own gaming experience. We believe every player\'s voice matters, and every review can help others make better game choices.'
    },
    'about_content_3': {
        'zh': '无论您是休闲玩家还是硬核游戏迷，无论您喜欢单机游戏还是多人在线游戏，这里都有适合您的内容。让我们一起探索游戏的无限可能！',
        'en': 'Whether you are a casual player or a hardcore gamer, whether you prefer single-player games or multiplayer online games, there is content here for you. Let\'s explore the infinite possibilities of gaming together!'
    },
    
    # 成就部分
    'achievements_title': {
        'zh': '🏆 我们的成就',
        'en': '🏆 Our Achievements'
    },
    'achievement_games': {
        'zh': '精选游戏',
        'en': 'Featured Games'
    },
    'achievement_ratings': {
        'zh': '用户评分',
        'en': 'User Ratings'
    },
    'achievement_comments': {
        'zh': '用户评论',
        'en': 'User Comments'
    },
    'achievement_users': {
        'zh': '活跃用户',
        'en': 'Active Users'
    },
    'achievement_satisfaction': {
        'zh': '用户满意度',
        'en': 'User Satisfaction'
    },
    'achievement_service': {
        'zh': '在线服务',
        'en': 'Online Service'
    },
    
    # 功能特色部分
    'features_title': {
        'zh': '✨ 功能特色',
        'en': '✨ Key Features'
    },
    'feature_rating_title': {
        'zh': '精准评分',
        'en': 'Accurate Rating'
    },
    'feature_rating_desc': {
        'zh': '基于真实用户体验的五星评分系统',
        'en': 'Five-star rating system based on real user experience'
    },
    'feature_comment_title': {
        'zh': '深度评论',
        'en': 'In-depth Reviews'
    },
    'feature_comment_desc': {
        'zh': '详细的游戏评论和玩法心得分享',
        'en': 'Detailed game reviews and gameplay insights sharing'
    },
    'feature_info_title': {
        'zh': '详细信息',
        'en': 'Detailed Information'
    },
    'feature_info_desc': {
        'zh': '全面的游戏信息和开发商资料',
        'en': 'Comprehensive game information and developer profiles'
    },
    'feature_responsive_title': {
        'zh': '响应式设计',
        'en': 'Responsive Design'
    },
    'feature_responsive_desc': {
        'zh': '完美适配手机、平板和电脑',
        'en': 'Perfect compatibility with mobile, tablet and desktop'
    },
    'feature_speed_title': {
        'zh': '快速加载',
        'en': 'Fast Loading'
    },
    'feature_speed_desc': {
        'zh': '优化的页面加载速度和用户体验',
        'en': 'Optimized page loading speed and user experience'
    },
    'feature_security_title': {
        'zh': '安全可靠',
        'en': 'Safe & Reliable'
    },
    'feature_security_desc': {
        'zh': '保护用户隐私和数据安全',
        'en': 'Protecting user privacy and data security'
    },
    
    # 游戏推荐部分
    'games_title': {
        'zh': '🔥 热门游戏推荐',
        'en': '🔥 Popular Game Recommendations'
    },
    
    # 游戏详情页面
    'game_info': {
        'zh': '游戏信息',
        'en': 'Game Information'
    },
    'genre': {
        'zh': '类型',
        'en': 'Genre'
    },
    'developer': {
        'zh': '开发商',
        'en': 'Developer'
    },
    'release_date': {
        'zh': '发布日期',
        'en': 'Release Date'
    },
    'description': {
        'zh': '游戏描述',
        'en': 'Description'
    },
    'gameplay': {
        'zh': '玩法介绍',
        'en': 'Gameplay'
    },
    'rating': {
        'zh': '评分',
        'en': 'Rating'
    },
    'average_rating': {
        'zh': '平均评分',
        'en': 'Average Rating'
    },
    'rate_this_game': {
        'zh': '为这款游戏评分',
        'en': 'Rate This Game'
    },
    'comments': {
        'zh': '用户评论',
        'en': 'User Comments'
    },
    'add_comment': {
        'zh': '发表评论',
        'en': 'Add Comment'
    },
    'username': {
        'zh': '用户名',
        'en': 'Username'
    },
    'comment': {
        'zh': '评论内容',
        'en': 'Comment'
    },
    'no_comments': {
        'zh': '暂无评论，快来发表第一条评论吧！',
        'en': 'No comments yet. Be the first to comment!'
    }
}

# 游戏数据翻译
GAMES_TRANSLATIONS = {
    1: {
        'title': {
            'zh': '塞尔达传说：旷野之息',
            'en': 'The Legend of Zelda: Breath of the Wild'
        },
        'genre': {
            'zh': '动作冒险',
            'en': 'Action Adventure'
        },
        'developer': {
            'zh': '任天堂',
            'en': 'Nintendo'
        },
        'description': {
            'zh': '《塞尔达传说：旷野之息》是一款开放世界动作冒险游戏，玩家将扮演林克，在一个广阔的海拉尔王国中探索、战斗和解谜。游戏以其创新的物理引擎和自由度高的游戏玩法而闻名。',
            'en': 'The Legend of Zelda: Breath of the Wild is an open-world action-adventure game where players take on the role of Link, exploring, fighting, and solving puzzles in the vast kingdom of Hyrule. The game is renowned for its innovative physics engine and high degree of freedom in gameplay.'
        },
        'gameplay': {
            'zh': '玩家可以自由探索开放世界，攀爬任何表面，使用各种武器和道具解决谜题，体验非线性的冒险旅程。游戏鼓励创造性解决问题和实验性玩法。',
            'en': 'Players can freely explore the open world, climb any surface, use various weapons and tools to solve puzzles, and experience a non-linear adventure journey. The game encourages creative problem-solving and experimental gameplay.'
        }
    },
    2: {
        'title': {
            'zh': '巫师3：狂猎',
            'en': 'The Witcher 3: Wild Hunt'
        },
        'genre': {
            'zh': '角色扮演',
            'en': 'Role-Playing Game'
        },
        'developer': {
            'zh': 'CD Projekt Red',
            'en': 'CD Projekt Red'
        },
        'description': {
            'zh': '《巫师3：狂猎》是一款角色扮演游戏，玩家将扮演杰洛特，一位猎魔人，在一个充满战争和怪物的世界中寻找自己的养女。',
            'en': 'The Witcher 3: Wild Hunt is a role-playing game where players take on the role of Geralt, a witcher, searching for his adopted daughter in a world filled with war and monsters.'
        },
        'gameplay': {
            'zh': '玩家通过完成主线任务和支线任务推进剧情，使用剑术、魔法和炼金术战斗，做出影响故事走向的道德选择。游戏拥有丰富的角色发展系统。',
            'en': 'Players advance the story by completing main and side quests, using swordplay, magic, and alchemy in combat, making moral choices that affect the story\'s direction. The game features a rich character development system.'
        }
    },
    3: {
        'title': {
            'zh': '艾尔登法环',
            'en': 'Elden Ring'
        },
        'genre': {
            'zh': '动作角色扮演',
            'en': 'Action RPG'
        },
        'developer': {
            'zh': 'FromSoftware',
            'en': 'FromSoftware'
        },
        'description': {
            'zh': '《艾尔登法环》是一款由FromSoftware开发的动作角色扮演游戏，以其开放世界设计和高难度的战斗系统而闻名。',
            'en': 'Elden Ring is an action role-playing game developed by FromSoftware, known for its open-world design and challenging combat system.'
        },
        'gameplay': {
            'zh': '玩家在开放世界中探索，面对具有挑战性的敌人和Boss，通过死亡学习敌人攻击模式，升级角色属性和装备，体验高难度但公平的战斗。',
            'en': 'Players explore an open world, facing challenging enemies and bosses, learning enemy attack patterns through death, upgrading character attributes and equipment, experiencing difficult but fair combat.'
        }
    },
    4: {
        'title': {
            'zh': '我的世界',
            'en': 'Minecraft'
        },
        'genre': {
            'zh': '沙盒',
            'en': 'Sandbox'
        },
        'developer': {
            'zh': 'Mojang Studios',
            'en': 'Mojang Studios'
        },
        'description': {
            'zh': '《我的世界》是一款沙盒游戏，玩家可以在一个由方块组成的3D世界中探索、采集资源、制作工具和建筑物。',
            'en': 'Minecraft is a sandbox game where players can explore, gather resources, craft tools and build structures in a 3D world made of blocks.'
        },
        'gameplay': {
            'zh': '玩家可以在生存模式中收集资源、建造庇护所、对抗怪物，或在创造模式中自由建造。游戏支持多人合作和模组扩展。',
            'en': 'Players can collect resources, build shelters, and fight monsters in survival mode, or build freely in creative mode. The game supports multiplayer cooperation and mod extensions.'
        }
    },
    5: {
        'title': {
            'zh': '英雄联盟',
            'en': 'League of Legends'
        },
        'genre': {
            'zh': 'MOBA',
            'en': 'MOBA'
        },
        'developer': {
            'zh': 'Riot Games',
            'en': 'Riot Games'
        },
        'description': {
            'zh': '《英雄联盟》是一款多人在线战术竞技游戏，玩家将扮演一名"英雄"，与队友合作击败敌方团队。',
            'en': 'League of Legends is a multiplayer online battle arena game where players take on the role of a "champion" and work with teammates to defeat the opposing team.'
        },
        'gameplay': {
            'zh': '两支五人队伍在召唤师峡谷中对战，目标是摧毁敌方基地。玩家选择不同英雄，通过击杀小兵和敌方英雄获得金币和经验，购买装备提升实力。',
            'en': 'Two five-player teams battle in Summoner\'s Rift with the goal of destroying the enemy base. Players choose different champions, gain gold and experience by killing minions and enemy champions, and purchase equipment to increase their strength.'
        }
    },
    6: {
        'title': {
            'zh': '荒野大镖客：救赎2',
            'en': 'Red Dead Redemption 2'
        },
        'genre': {
            'zh': '动作冒险',
            'en': 'Action Adventure'
        },
        'developer': {
            'zh': 'Rockstar Games',
            'en': 'Rockstar Games'
        },
        'description': {
            'zh': '《荒野大镖客：救赎2》是一款开放世界动作冒险游戏，设定在美国西部的末日。',
            'en': 'Red Dead Redemption 2 is an open-world action-adventure game set in the dying days of the American West.'
        },
        'gameplay': {
            'zh': '玩家扮演亚瑟·摩根，体验西部牛仔生活，参与抢劫、狩猎、钓鱼等活动。游戏注重角色互动和道德选择，拥有详细的荣誉系统。',
            'en': 'Players take on the role of Arthur Morgan, experiencing cowboy life in the West, participating in robberies, hunting, fishing and other activities. The game focuses on character interaction and moral choices, with a detailed honor system.'
        }
    },
    7: {
        'title': {
            'zh': '赛博朋克2077',
            'en': 'Cyberpunk 2077'
        },
        'genre': {
            'zh': '动作角色扮演',
            'en': 'Action RPG'
        },
        'developer': {
            'zh': 'CD Projekt Red',
            'en': 'CD Projekt Red'
        },
        'description': {
            'zh': '《赛博朋克2077》是一款开放世界动作角色扮演游戏，设定在未来的夜之城。',
            'en': 'Cyberpunk 2077 is an open-world action role-playing game set in the futuristic Night City.'
        },
        'gameplay': {
            'zh': '玩家扮演V，在赛博朋克世界中完成任务，使用高科技武器和义体改造，体验分支剧情和多种结局。',
            'en': 'Players take on the role of V, completing missions in a cyberpunk world, using high-tech weapons and cybernetic enhancements, experiencing branching storylines and multiple endings.'
        }
    },
    8: {
        'title': {
            'zh': '原神',
            'en': 'Genshin Impact'
        },
        'genre': {
            'zh': '动作角色扮演',
            'en': 'Action RPG'
        },
        'developer': {
            'zh': 'miHoYo',
            'en': 'miHoYo'
        },
        'description': {
            'zh': '《原神》是一款开放世界动作角色扮演游戏，玩家在提瓦特大陆上冒险。',
            'en': 'Genshin Impact is an open-world action role-playing game where players adventure in the continent of Teyvat.'
        },
        'gameplay': {
            'zh': '玩家收集不同元素的角色，组成队伍探索世界，使用元素反应战斗，完成主线和日常任务，参与限时活动。',
            'en': 'Players collect characters of different elements, form teams to explore the world, use elemental reactions in combat, complete main and daily quests, and participate in limited-time events.'
        }
    },
    9: {
        'title': {
            'zh': 'DOTA 2',
            'en': 'DOTA 2'
        },
        'genre': {
            'zh': 'MOBA',
            'en': 'MOBA'
        },
        'developer': {
            'zh': 'Valve',
            'en': 'Valve'
        },
        'description': {
            'zh': '《DOTA 2》是一款多人在线战术竞技游戏，两支队伍争夺古代遗迹的控制权。',
            'en': 'DOTA 2 is a multiplayer online battle arena game where two teams compete for control of the Ancient.'
        },
        'gameplay': {
            'zh': '玩家选择英雄，通过击杀敌方单位获得金币和经验，购买装备，与队友配合摧毁敌方古代遗迹。游戏策略性极强。',
            'en': 'Players choose heroes, gain gold and experience by killing enemy units, purchase equipment, and work with teammates to destroy the enemy Ancient. The game is highly strategic.'
        }
    },
    10: {
        'title': {
            'zh': '守望先锋2',
            'en': 'Overwatch 2'
        },
        'genre': {
            'zh': '第一人称射击',
            'en': 'First-Person Shooter'
        },
        'developer': {
            'zh': '暴雪娱乐',
            'en': 'Blizzard Entertainment'
        },
        'description': {
            'zh': '《守望先锋2》是一款团队射击游戏，玩家选择不同英雄进行5v5对战。',
            'en': 'Overwatch 2 is a team-based shooter where players choose different heroes for 5v5 battles.'
        },
        'gameplay': {
            'zh': '玩家选择坦克、输出或支援英雄，与队友配合完成目标。游戏强调团队合作和英雄技能的战术运用。',
            'en': 'Players choose tank, damage, or support heroes and work with teammates to complete objectives. The game emphasizes teamwork and tactical use of hero abilities.'
        }
    }
}

def get_text(key, lang='zh'):
    """获取指定语言的文本"""
    return TRANSLATIONS.get(key, {}).get(lang, TRANSLATIONS.get(key, {}).get('zh', key))

def get_game_text(game_id, field, lang='zh'):
    """获取指定语言的游戏文本"""
    return GAMES_TRANSLATIONS.get(game_id, {}).get(field, {}).get(lang, 
           GAMES_TRANSLATIONS.get(game_id, {}).get(field, {}).get('zh', ''))
