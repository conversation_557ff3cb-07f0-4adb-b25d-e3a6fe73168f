import http.server
import socketserver
import json
import os
import urllib.parse
from datetime import datetime
from languages import get_text, get_game_text, LANGUAGES

# 创建数据目录
os.makedirs('game_data', exist_ok=True)

# 游戏数据库
GAMES_DB = {
    1: {
        'id': 1,
        'title': '塞尔达传说：旷野之息',
        'genre': '动作冒险',
        'developer': '任天堂',
        'release_date': '2017-03-03',
        'image': '/static/zelda_botw.svg',
        'description': '《塞尔达传说：旷野之息》是一款开放世界动作冒险游戏，玩家将扮演林克，在一个广阔的海拉尔王国中探索、战斗和解谜。游戏以其创新的物理引擎和自由度高的游戏玩法而闻名。',
        'gameplay': '玩家可以自由探索开放世界，攀爬任何表面，使用各种武器和道具解决谜题，体验非线性的冒险旅程。游戏鼓励创造性解决问题和实验性玩法。'
    },
    2: {
        'id': 2,
        'title': '巫师3：狂猎',
        'genre': '角色扮演',
        'developer': 'CD Projekt Red',
        'release_date': '2015-05-19',
        'image': '/static/witcher3.svg',
        'description': '《巫师3：狂猎》是一款角色扮演游戏，玩家将扮演杰洛特，一位猎魔人，在一个充满战争和怪物的世界中寻找自己的养女。',
        'gameplay': '玩家通过完成主线任务和支线任务推进剧情，使用剑术、魔法和炼金术战斗，做出影响故事走向的道德选择。游戏拥有丰富的角色发展系统。'
    },
    3: {
        'id': 3,
        'title': '艾尔登法环',
        'genre': '动作角色扮演',
        'developer': 'FromSoftware',
        'release_date': '2022-02-25',
        'image': '/static/elden_ring_official.jpg',
        'description': '《艾尔登法环》是一款由FromSoftware开发的动作角色扮演游戏，以其开放世界设计和高难度的战斗系统而闻名。',
        'gameplay': '玩家在开放世界中探索，面对具有挑战性的敌人和Boss，通过死亡学习敌人攻击模式，升级角色属性和装备，体验高难度但公平的战斗。'
    },
    4: {
        'id': 4,
        'title': '我的世界',
        'genre': '沙盒',
        'developer': 'Mojang Studios',
        'release_date': '2011-11-18',
        'image': '/static/minecraft.svg',
        'description': '《我的世界》是一款沙盒游戏，玩家可以在一个由方块组成的3D世界中探索、采集资源、制作工具和建筑物。',
        'gameplay': '玩家可以在生存模式中收集资源、建造庇护所、对抗怪物，或在创造模式中自由建造。游戏支持多人合作和模组扩展。'
    },
    5: {
        'id': 5,
        'title': '英雄联盟',
        'genre': 'MOBA',
        'developer': 'Riot Games',
        'release_date': '2009-10-27',
        'image': '/static/lol.svg',
        'description': '《英雄联盟》是一款多人在线战术竞技游戏，玩家将扮演一名"英雄"，与队友合作击败敌方团队。',
        'gameplay': '两支五人队伍在召唤师峡谷中对战，目标是摧毁敌方基地。玩家选择不同英雄，通过击杀小兵和敌方英雄获得金币和经验，购买装备提升实力。'
    },
    6: {
        'id': 6,
        'title': '荒野大镖客：救赎2',
        'genre': '动作冒险',
        'developer': 'Rockstar Games',
        'release_date': '2018-10-26',
        'image': '/static/rdr2.svg',
        'description': '《荒野大镖客：救赎2》是一款开放世界动作冒险游戏，设定在美国西部的末日。',
        'gameplay': '玩家扮演亚瑟·摩根，体验西部牛仔生活，参与抢劫、狩猎、钓鱼等活动。游戏注重角色互动和道德选择，拥有详细的荣誉系统。'
    },
    7: {
        'id': 7,
        'title': '赛博朋克2077',
        'genre': '动作角色扮演',
        'developer': 'CD Projekt Red',
        'release_date': '2020-12-10',
        'image': '/static/cyberpunk2077.svg',
        'description': '《赛博朋克2077》是一款开放世界动作角色扮演游戏，设定在未来的夜之城。',
        'gameplay': '玩家扮演V，在赛博朋克世界中完成任务，使用高科技武器和义体改造，体验分支剧情和多种结局。'
    },
    8: {
        'id': 8,
        'title': '原神',
        'genre': '动作角色扮演',
        'developer': 'miHoYo',
        'release_date': '2020-09-28',
        'image': '/static/genshin.svg',
        'description': '《原神》是一款开放世界动作角色扮演游戏，玩家在提瓦特大陆上冒险。',
        'gameplay': '玩家收集不同元素的角色，组成队伍探索世界，使用元素反应战斗，完成主线和日常任务，参与限时活动。'
    },
    9: {
        'id': 9,
        'title': 'DOTA 2',
        'genre': 'MOBA',
        'developer': 'Valve',
        'release_date': '2013-07-09',
        'image': '/static/dota2.svg',
        'description': '《DOTA 2》是一款多人在线战术竞技游戏，两支队伍争夺古代遗迹的控制权。',
        'gameplay': '玩家选择英雄，通过击杀敌方单位获得金币和经验，购买装备，与队友配合摧毁敌方古代遗迹。游戏策略性极强。'
    },
    10: {
        'id': 10,
        'title': '守望先锋2',
        'genre': '第一人称射击',
        'developer': '暴雪娱乐',
        'release_date': '2022-10-04',
        'image': '/static/overwatch2.svg',
        'description': '《守望先锋2》是一款团队射击游戏，玩家选择不同英雄进行5v5对战。',
        'gameplay': '玩家选择坦克、输出或支援英雄，与队友配合完成目标。游戏强调团队合作和英雄技能的战术运用。'
    }
}

# 评分和评论数据
ratings_file = 'game_data/ratings.json'
comments_file = 'game_data/comments.json'

def load_data(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        return {}

def save_data(filename, data):
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def get_average_rating(game_id):
    ratings = load_data(ratings_file)
    game_ratings = ratings.get(str(game_id), [])
    if not game_ratings:
        return 0
    return sum(game_ratings) / len(game_ratings)

def get_comments(game_id):
    comments = load_data(comments_file)
    return comments.get(str(game_id), [])

class GameHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path

        if path == '/' or path == '/index.html':
            self.serve_home_page()
        elif path.startswith('/game/'):
            game_id = path.split('/')[-1]
            if game_id.isdigit():
                self.serve_game_detail(int(game_id))
            else:
                self.send_error(404)
        elif path.startswith('/static/'):
            self.serve_static_file(path)
        else:
            self.send_error(404)

    def do_POST(self):
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path

        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')
        form_data = urllib.parse.parse_qs(post_data)

        if path.startswith('/rate/'):
            game_id = path.split('/')[-1]
            if game_id.isdigit():
                self.handle_rating(int(game_id), form_data)
        elif path.startswith('/comment/'):
            game_id = path.split('/')[-1]
            if game_id.isdigit():
                self.handle_comment(int(game_id), form_data)

    def get_language_from_url(self):
        """从URL参数中获取语言设置"""
        parsed_path = urllib.parse.urlparse(self.path)
        query_params = urllib.parse.parse_qs(parsed_path.query)
        return query_params.get('lang', ['zh'])[0]

    def serve_home_page(self):
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()

        # 获取当前语言
        current_lang = self.get_language_from_url()

        games_html = ""
        for game in GAMES_DB.values():
            avg_rating = get_average_rating(game['id'])
            stars = "★" * int(avg_rating) + "☆" * (5 - int(avg_rating))

            # 获取多语言文本
            game_title = get_game_text(game['id'], 'title', current_lang) or game['title']
            game_genre = get_game_text(game['id'], 'genre', current_lang) or game['genre']
            game_developer = get_game_text(game['id'], 'developer', current_lang) or game['developer']
            game_description = get_game_text(game['id'], 'description', current_lang) or game['description']
            view_details_text = get_text('view_details', current_lang)

            games_html += f'''
            <div class="game-card">
                <div class="game-image">
                    <img src="{game['image']}" alt="{game_title}">
                </div>
                <div class="game-info">
                    <h3>{game_title}</h3>
                    <p class="genre">{game_genre} | {game_developer}</p>
                    <div class="rating">
                        <span class="stars">{stars}</span>
                        <span class="rating-value">({avg_rating:.1f}/5.0)</span>
                    </div>
                    <p class="description">{game_description[:100]}...</p>
                    <a href="/game/{game['id']}?lang={current_lang}" class="btn">{view_details_text}</a>
                </div>
            </div>
            '''

        html = f'''
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>热门游戏评分网站</title>
            <style>
                body {{
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                    background-color: #f5f5f5;
                }}
                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 2rem 0;
                    text-align: center;
                }}
                .container {{
                    max-width: 1200px;
                    margin: 0 auto;
                    padding: 2rem;
                }}
                .games-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
                    gap: 2rem;
                    margin-top: 2rem;
                }}
                .game-card {{
                    background: white;
                    border-radius: 10px;
                    overflow: hidden;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                    transition: transform 0.3s ease;
                }}
                .game-card:hover {{
                    transform: translateY(-5px);
                }}
                .game-image {{
                    height: 200px;
                    overflow: hidden;
                }}
                .game-image img {{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }}
                .game-info {{
                    padding: 1.5rem;
                }}
                .game-info h3 {{
                    margin: 0 0 0.5rem 0;
                    color: #333;
                }}
                .genre {{
                    color: #666;
                    font-size: 0.9rem;
                    margin-bottom: 0.5rem;
                }}
                .rating {{
                    margin-bottom: 1rem;
                }}
                .stars {{
                    color: #ffd700;
                    font-size: 1.2rem;
                }}
                .rating-value {{
                    color: #666;
                    margin-left: 0.5rem;
                }}
                .description {{
                    color: #555;
                    line-height: 1.5;
                    margin-bottom: 1rem;
                }}
                .btn {{
                    display: inline-block;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 0.75rem 1.5rem;
                    text-decoration: none;
                    border-radius: 5px;
                    transition: opacity 0.3s ease;
                }}
                .btn:hover {{
                    opacity: 0.9;
                }}
                .intro-section {{
                    background: white;
                    border-radius: 10px;
                    padding: 2rem;
                    margin-bottom: 2rem;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                }}
                .intro-section h2 {{
                    color: #333;
                    margin-bottom: 1rem;
                    border-bottom: 3px solid #667eea;
                    padding-bottom: 0.5rem;
                    display: inline-block;
                }}
                .intro-content {{
                    color: #555;
                    line-height: 1.6;
                    font-size: 1.1rem;
                }}
                .achievements-section {{
                    background: white;
                    border-radius: 10px;
                    padding: 2rem;
                    margin-bottom: 2rem;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                }}
                .achievements-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                    gap: 1.5rem;
                    margin-top: 1.5rem;
                }}
                .achievement-card {{
                    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                    color: white;
                    padding: 1.5rem;
                    border-radius: 8px;
                    text-align: center;
                    transition: transform 0.3s ease;
                }}
                .achievement-card:hover {{
                    transform: translateY(-3px);
                }}
                .achievement-icon {{
                    font-size: 2.5rem;
                    margin-bottom: 1rem;
                }}
                .achievement-number {{
                    font-size: 2rem;
                    font-weight: bold;
                    margin-bottom: 0.5rem;
                }}
                .achievement-label {{
                    font-size: 1rem;
                    opacity: 0.9;
                }}
                .features-section {{
                    background: white;
                    border-radius: 10px;
                    padding: 2rem;
                    margin-bottom: 2rem;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                }}
                .features-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 1.5rem;
                    margin-top: 1.5rem;
                }}
                .feature-item {{
                    text-align: center;
                    padding: 1rem;
                }}
                .feature-icon {{
                    font-size: 3rem;
                    color: #667eea;
                    margin-bottom: 1rem;
                }}
                .feature-title {{
                    font-size: 1.2rem;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 0.5rem;
                }}
                .feature-desc {{
                    color: #666;
                    font-size: 0.9rem;
                }}
                .language-switcher {{
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1000;
                    background: rgba(255, 255, 255, 0.95);
                    border-radius: 25px;
                    padding: 10px 15px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    backdrop-filter: blur(10px);
                }}
                .language-switcher select {{
                    border: none;
                    background: transparent;
                    font-size: 14px;
                    font-weight: bold;
                    color: #333;
                    cursor: pointer;
                    outline: none;
                }}
                .language-switcher select option {{
                    background: white;
                    color: #333;
                }}
            </style>
        </head>
        <body>
            <!-- 语言切换器 -->
            <div class="language-switcher">
                <select onchange="changeLanguage(this.value)">
                    <option value="zh" {'selected' if current_lang == 'zh' else ''}>🇨🇳 中文</option>
                    <option value="en" {'selected' if current_lang == 'en' else ''}>🇺🇸 English</option>
                </select>
            </div>

            <div class="header">
                <h1>{get_text('site_title', current_lang)}</h1>
                <p>{get_text('site_subtitle', current_lang)}</p>
            </div>

            <div class="container">
                <!-- 网站介绍部分 -->
                <div class="intro-section">
                    <h2>{get_text('about_title', current_lang)}</h2>
                    <div class="intro-content">
                        <p>{get_text('about_content_1', current_lang)}</p>
                        <p>{get_text('about_content_2', current_lang)}</p>
                        <p>{get_text('about_content_3', current_lang)}</p>
                    </div>
                </div>

                <!-- 网站成就展示 -->
                <div class="achievements-section">
                    <h2>{get_text('achievements_title', current_lang)}</h2>
                    <div class="achievements-grid">
                        <div class="achievement-card">
                            <div class="achievement-icon">🎮</div>
                            <div class="achievement-number">10+</div>
                            <div class="achievement-label">{get_text('achievement_games', current_lang)}</div>
                        </div>
                        <div class="achievement-card">
                            <div class="achievement-icon">⭐</div>
                            <div class="achievement-number">500+</div>
                            <div class="achievement-label">{get_text('achievement_ratings', current_lang)}</div>
                        </div>
                        <div class="achievement-card">
                            <div class="achievement-icon">💬</div>
                            <div class="achievement-number">1000+</div>
                            <div class="achievement-label">{get_text('achievement_comments', current_lang)}</div>
                        </div>
                        <div class="achievement-card">
                            <div class="achievement-icon">👥</div>
                            <div class="achievement-number">2000+</div>
                            <div class="achievement-label">{get_text('achievement_users', current_lang)}</div>
                        </div>
                        <div class="achievement-card">
                            <div class="achievement-icon">🔥</div>
                            <div class="achievement-number">95%</div>
                            <div class="achievement-label">{get_text('achievement_satisfaction', current_lang)}</div>
                        </div>
                        <div class="achievement-card">
                            <div class="achievement-icon">🌟</div>
                            <div class="achievement-number">24/7</div>
                            <div class="achievement-label">{get_text('achievement_service', current_lang)}</div>
                        </div>
                    </div>
                </div>

                <!-- 功能特色 -->
                <div class="features-section">
                    <h2>{get_text('features_title', current_lang)}</h2>
                    <div class="features-grid">
                        <div class="feature-item">
                            <div class="feature-icon">🎯</div>
                            <div class="feature-title">{get_text('feature_rating_title', current_lang)}</div>
                            <div class="feature-desc">{get_text('feature_rating_desc', current_lang)}</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">💭</div>
                            <div class="feature-title">{get_text('feature_comment_title', current_lang)}</div>
                            <div class="feature-desc">{get_text('feature_comment_desc', current_lang)}</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">🔍</div>
                            <div class="feature-title">{get_text('feature_info_title', current_lang)}</div>
                            <div class="feature-desc">{get_text('feature_info_desc', current_lang)}</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">📱</div>
                            <div class="feature-title">{get_text('feature_responsive_title', current_lang)}</div>
                            <div class="feature-desc">{get_text('feature_responsive_desc', current_lang)}</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">🚀</div>
                            <div class="feature-title">{get_text('feature_speed_title', current_lang)}</div>
                            <div class="feature-desc">{get_text('feature_speed_desc', current_lang)}</div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">🛡️</div>
                            <div class="feature-title">{get_text('feature_security_title', current_lang)}</div>
                            <div class="feature-desc">{get_text('feature_security_desc', current_lang)}</div>
                        </div>
                    </div>
                </div>

                <!-- 热门游戏推荐 -->
                <h2>{get_text('games_title', current_lang)}</h2>
                <div class="games-grid">
                    {games_html}
                </div>
            </div>

            <script>
                function changeLanguage(lang) {{
                    const currentUrl = new URL(window.location);
                    currentUrl.searchParams.set('lang', lang);
                    window.location.href = currentUrl.toString();
                }}
            </script>
        </body>
        </html>
        '''

        self.wfile.write(html.encode('utf-8'))

    def serve_game_detail(self, game_id):
        if game_id not in GAMES_DB:
            self.send_error(404)
            return

        # 获取当前语言
        current_lang = self.get_language_from_url()

        game = GAMES_DB[game_id]
        avg_rating = get_average_rating(game_id)
        comments = get_comments(game_id)

        stars = "★" * int(avg_rating) + "☆" * (5 - int(avg_rating))

        # 获取多语言游戏信息
        game_title = get_game_text(game_id, 'title', current_lang) or game['title']
        game_genre = get_game_text(game_id, 'genre', current_lang) or game['genre']
        game_developer = get_game_text(game_id, 'developer', current_lang) or game['developer']
        game_description = get_game_text(game_id, 'description', current_lang) or game['description']
        game_gameplay = get_game_text(game_id, 'gameplay', current_lang) or game['gameplay']

        comments_html = ""
        for comment in comments:
            comments_html += f'''
            <div class="comment">
                <div class="comment-header">
                    <strong>{comment['username']}</strong>
                    <span class="comment-date">{comment['date']}</span>
                </div>
                <p>{comment['content']}</p>
            </div>
            '''

        if not comments_html:
            comments_html = f"<p>{get_text('no_comments', current_lang)}</p>"

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()

        html = f'''
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>{game_title} - {get_text('game_info', current_lang)}</title>
            <style>
                body {{
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                    background-color: #f5f5f5;
                }}
                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 1rem 0;
                }}
                .nav {{
                    text-align: center;
                }}
                .nav a {{
                    color: white;
                    text-decoration: none;
                    font-size: 1.2rem;
                }}
                .container {{
                    max-width: 1000px;
                    margin: 0 auto;
                    padding: 2rem;
                }}
                .game-detail {{
                    background: white;
                    border-radius: 10px;
                    overflow: hidden;
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                    margin-bottom: 2rem;
                }}
                .game-header {{
                    display: flex;
                    padding: 2rem;
                }}
                .game-image {{
                    width: 300px;
                    height: 200px;
                    margin-right: 2rem;
                    border-radius: 10px;
                    overflow: hidden;
                }}
                .game-image img {{
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                }}
                .game-info {{
                    flex: 1;
                }}
                .game-info h1 {{
                    margin: 0 0 1rem 0;
                    color: #333;
                }}
                .game-meta {{
                    color: #666;
                    margin-bottom: 1rem;
                }}
                .rating {{
                    margin-bottom: 1rem;
                }}
                .stars {{
                    color: #ffd700;
                    font-size: 1.5rem;
                }}
                .rating-value {{
                    color: #666;
                    margin-left: 0.5rem;
                    font-size: 1.1rem;
                }}
                .gameplay {{
                    background: #f8f9fa;
                    padding: 2rem;
                    border-top: 1px solid #eee;
                }}
                .interaction-section {{
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 2rem;
                }}
                .rating-section, .comment-section {{
                    background: white;
                    padding: 2rem;
                    border-radius: 10px;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                .form-group {{
                    margin-bottom: 1rem;
                }}
                .form-group label {{
                    display: block;
                    margin-bottom: 0.5rem;
                    font-weight: bold;
                }}
                .form-control {{
                    width: 100%;
                    padding: 0.75rem;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    font-size: 1rem;
                }}
                .btn {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 1rem;
                }}
                .btn:hover {{
                    opacity: 0.9;
                }}
                .comment {{
                    background: #f8f9fa;
                    padding: 1rem;
                    border-radius: 5px;
                    margin-bottom: 1rem;
                }}
                .comment-header {{
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 0.5rem;
                }}
                .comment-date {{
                    color: #666;
                    font-size: 0.9rem;
                }}
                .language-switcher {{
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 1000;
                    background: rgba(255, 255, 255, 0.95);
                    border-radius: 25px;
                    padding: 10px 15px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    backdrop-filter: blur(10px);
                }}
                .language-switcher select {{
                    border: none;
                    background: transparent;
                    font-size: 14px;
                    font-weight: bold;
                    color: #333;
                    cursor: pointer;
                    outline: none;
                }}
            </style>
        </head>
        <body>
            <!-- 语言切换器 -->
            <div class="language-switcher">
                <select onchange="changeLanguage(this.value)">
                    <option value="zh" {'selected' if current_lang == 'zh' else ''}>🇨🇳 中文</option>
                    <option value="en" {'selected' if current_lang == 'en' else ''}>🇺🇸 English</option>
                </select>
            </div>

            <div class="header">
                <div class="nav">
                    <a href="/?lang={current_lang}">← {get_text('back_to_home', current_lang)}</a>
                </div>
            </div>

            <div class="container">
                <div class="game-detail">
                    <div class="game-header">
                        <div class="game-image">
                            <img src="{game['image']}" alt="{game_title}">
                        </div>
                        <div class="game-info">
                            <h1>{game_title}</h1>
                            <div class="game-meta">
                                <p><strong>{get_text('genre', current_lang)}:</strong> {game_genre}</p>
                                <p><strong>{get_text('developer', current_lang)}:</strong> {game_developer}</p>
                                <p><strong>{get_text('release_date', current_lang)}:</strong> {game['release_date']}</p>
                            </div>
                            <div class="rating">
                                <span class="stars">{stars}</span>
                                <span class="rating-value">({avg_rating:.1f}/5.0)</span>
                            </div>
                            <p>{game_description}</p>
                        </div>
                    </div>
                    <div class="gameplay">
                        <h3>🎮 {get_text('gameplay', current_lang)}</h3>
                        <p>{game_gameplay}</p>
                    </div>
                </div>

                <div class="interaction-section">
                    <div class="rating-section">
                        <h3>⭐ {get_text('rate_this_game', current_lang)}</h3>
                        <form action="/rate/{game_id}" method="post">
                            <input type="hidden" name="lang" value="{current_lang}">
                            <div class="form-group">
                                <label for="username">{get_text('username', current_lang)}:</label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="form-group">
                                <label for="rating">{get_text('rating', current_lang)} (1-5星):</label>
                                <select class="form-control" name="rating" required>
                                    <option value="">{get_text('rating', current_lang) if current_lang == 'zh' else 'Select Rating'}</option>
                                    <option value="1">1星 - {'很差' if current_lang == 'zh' else 'Poor'}</option>
                                    <option value="2">2星 - {'较差' if current_lang == 'zh' else 'Fair'}</option>
                                    <option value="3">3星 - {'一般' if current_lang == 'zh' else 'Good'}</option>
                                    <option value="4">4星 - {'不错' if current_lang == 'zh' else 'Very Good'}</option>
                                    <option value="5">5星 - {'很棒' if current_lang == 'zh' else 'Excellent'}</option>
                                </select>
                            </div>
                            <button type="submit" class="btn">{get_text('submit_rating', current_lang)}</button>
                        </form>
                    </div>

                    <div class="comment-section">
                        <h3>💬 {get_text('comments', current_lang)}</h3>
                        <form action="/comment/{game_id}" method="post">
                            <input type="hidden" name="lang" value="{current_lang}">
                            <div class="form-group">
                                <label for="username">{get_text('username', current_lang)}:</label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="form-group">
                                <label for="comment">{get_text('comment', current_lang)}:</label>
                                <textarea class="form-control" name="comment" rows="3" required></textarea>
                            </div>
                            <button type="submit" class="btn">{get_text('submit_comment', current_lang)}</button>
                        </form>

                        <div style="margin-top: 2rem;">
                            <h4>{get_text('comments', current_lang)}:</h4>
                            {comments_html}
                        </div>
                    </div>
                </div>
            </div>

            <script>
                function changeLanguage(lang) {{
                    const currentUrl = new URL(window.location);
                    currentUrl.searchParams.set('lang', lang);
                    window.location.href = currentUrl.toString();
                }}
            </script>
        </body>
        </html>
        '''

        self.wfile.write(html.encode('utf-8'))

    def handle_rating(self, game_id, form_data):
        username = form_data.get('username', [''])[0]
        rating = form_data.get('rating', [''])[0]
        lang = form_data.get('lang', ['zh'])[0]

        if username and rating and rating.isdigit():
            ratings = load_data(ratings_file)
            if str(game_id) not in ratings:
                ratings[str(game_id)] = []
            ratings[str(game_id)].append(int(rating))
            save_data(ratings_file, ratings)

        self.send_response(302)
        self.send_header('Location', f'/game/{game_id}?lang={lang}')
        self.end_headers()

    def handle_comment(self, game_id, form_data):
        username = form_data.get('username', [''])[0]
        comment_text = form_data.get('comment', [''])[0]
        lang = form_data.get('lang', ['zh'])[0]

        if username and comment_text:
            comments = load_data(comments_file)
            if str(game_id) not in comments:
                comments[str(game_id)] = []

            comment = {
                'username': username,
                'content': comment_text,
                'date': datetime.now().strftime('%Y-%m-%d %H:%M')
            }
            comments[str(game_id)].append(comment)
            save_data(comments_file, comments)

        self.send_response(302)
        self.send_header('Location', f'/game/{game_id}?lang={lang}')
        self.end_headers()

    def serve_static_file(self, path):
        import mimetypes
        import os

        # 移除开头的 /static/
        file_path = path[8:]  # 去掉 '/static/'
        full_path = os.path.join('static', file_path)

        if os.path.exists(full_path):
            self.send_response(200)
            content_type, _ = mimetypes.guess_type(full_path)
            if content_type:
                self.send_header('Content-type', content_type)
            self.end_headers()

            with open(full_path, 'rb') as f:
                self.wfile.write(f.read())
        else:
            self.send_error(404)

# 启动服务器
PORT = 8082
Handler = GameHandler
httpd = socketserver.TCPServer(("", PORT), Handler)

print(f"🎮 游戏评分网站已启动！")
print(f"请访问 http://localhost:{PORT}/")
print("功能包括：")
print("- 浏览热门游戏列表")
print("- 查看游戏详细信息和玩法介绍")
print("- 为游戏评分")
print("- 发表游戏评论")

httpd.serve_forever()
