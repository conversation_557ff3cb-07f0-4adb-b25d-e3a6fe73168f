import http.server
import socketserver
import os

# 创建静态文件目录
os.makedirs('web', exist_ok=True)

# 创建一个简单的 HTML 文件
with open('web/index.html', 'w', encoding='utf-8') as f:
    f.write('''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>游戏网站</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .game {
            display: flex;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            padding: 10px;
            border-radius: 5px;
        }
        .game-image {
            width: 200px;
            height: 150px;
            margin-right: 20px;
            border-radius: 8px;
            overflow: hidden;
        }
        .game-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .game-info {
            flex: 1;
        }
        h1 {
            color: #2c3e50;
        }
        h2 {
            margin-top: 0;
            color: #3498db;
        }
    </style>
</head>
<body>
    <h1>热门游戏介绍</h1>

    <div class="game">
        <div class="game-image">
            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/3a/Zelda_Breath_of_the_Wild_Link_climbing.jpg/512px-Zelda_Breath_of_the_Wild_Link_climbing.jpg" alt="塞尔达传说：旷野之息">
        </div>
        <div class="game-info">
            <h2>塞尔达传说：旷野之息</h2>
            <p><strong>类型:</strong> 动作冒险</p>
            <p><strong>开发商:</strong> 任天堂</p>
            <p>《塞尔达传说：旷野之息》是一款开放世界动作冒险游戏，玩家将扮演林克，在一个广阔的海拉尔王国中探索、战斗和解谜。游戏以其创新的物理引擎和自由度高的游戏玩法而闻名。</p>
        </div>
    </div>

    <div class="game">
        <div class="game-image">
            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/e/e9/The_Witcher_3_Wild_Hunt_Geralt_of_Rivia.jpg/512px-The_Witcher_3_Wild_Hunt_Geralt_of_Rivia.jpg" alt="巫师3：狂猎">
        </div>
        <div class="game-info">
            <h2>巫师3：狂猎</h2>
            <p><strong>类型:</strong> 角色扮演</p>
            <p><strong>开发商:</strong> CD Projekt Red</p>
            <p>《巫师3：狂猎》是一款角色扮演游戏，玩家将扮演杰洛特，一位猎魔人，在一个充满战争和怪物的世界中寻找自己的养女。游戏以其丰富的剧情、精美的画面和复杂的道德选择系统而著称。</p>
        </div>
    </div>

    <div class="game">
        <div class="game-image">
            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/a/a2/Elden_Ring_gameplay_screenshot.jpg/512px-Elden_Ring_gameplay_screenshot.jpg" alt="艾尔登法环">
        </div>
        <div class="game-info">
            <h2>艾尔登法环</h2>
            <p><strong>类型:</strong> 动作角色扮演</p>
            <p><strong>开发商:</strong> FromSoftware</p>
            <p>《艾尔登法环》是一款由FromSoftware开发的动作角色扮演游戏，以其开放世界设计和高难度的战斗系统而闻名。玩家将在一个被称为"间界"的奇幻世界中探索，与各种强大的敌人战斗。</p>
        </div>
    </div>

    <div class="game">
        <div class="game-image">
            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/d/d2/Minecraft_gameplay_screenshot.jpg/512px-Minecraft_gameplay_screenshot.jpg" alt="我的世界">
        </div>
        <div class="game-info">
            <h2>我的世界</h2>
            <p><strong>类型:</strong> 沙盒</p>
            <p><strong>开发商:</strong> Mojang Studios</p>
            <p>《我的世界》是一款沙盒游戏，玩家可以在一个由方块组成的3D世界中探索、采集资源、制作工具和建筑物。游戏没有特定的目标，玩家可以自由地创造和探索。</p>
        </div>
    </div>

    <div class="game">
        <div class="game-image">
            <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/b/b5/League_of_Legends_gameplay_screenshot.jpg/512px-League_of_Legends_gameplay_screenshot.jpg" alt="英雄联盟">
        </div>
        <div class="game-info">
            <h2>英雄联盟</h2>
            <p><strong>类型:</strong> MOBA</p>
            <p><strong>开发商:</strong> Riot Games</p>
            <p>《英雄联盟》是一款多人在线战术竞技游戏，玩家将扮演一名"英雄"，与队友合作击败敌方团队。游戏以其竞技性和团队合作精神而闻名。</p>
        </div>
    </div>
</body>
</html>
''')

# 设置工作目录
os.chdir('web')

# 启动简单的 HTTP 服务器
PORT = 8080
Handler = http.server.SimpleHTTPRequestHandler
httpd = socketserver.TCPServer(("", PORT), Handler)

print(f"服务器已启动，请访问 http://localhost:{PORT}/")
httpd.serve_forever()
