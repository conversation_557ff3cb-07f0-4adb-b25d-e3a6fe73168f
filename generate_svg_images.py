#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏SVG图片生成器
为游戏网站生成SVG格式的占位符图片
"""

import os

def create_svg_image(game_name, filename, bg_color="#4080ff"):
    """创建SVG格式的游戏占位符图片"""
    
    # SVG模板
    svg_template = f'''<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景 -->
  <rect width="400" height="300" fill="{bg_color}"/>
  
  <!-- 渐变背景 -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:{bg_color};stop-opacity:1" />
      <stop offset="100%" style="stop-color:#000000;stop-opacity:0.3" />
    </linearGradient>
  </defs>
  <rect width="400" height="300" fill="url(#grad1)"/>
  
  <!-- 装饰边框 -->
  <rect x="10" y="10" width="380" height="280" fill="none" stroke="white" stroke-width="3"/>
  
  <!-- 游戏名称 -->
  <text x="200" y="140" font-family="Microsoft YaHei, Arial, sans-serif" 
        font-size="28" font-weight="bold" fill="white" text-anchor="middle"
        stroke="black" stroke-width="1">{game_name}</text>
  
  <!-- 标识 -->
  <text x="200" y="270" font-family="Microsoft YaHei, Arial, sans-serif" 
        font-size="14" fill="rgba(255,255,255,0.8)" text-anchor="middle">游戏图片</text>
  
  <!-- 装饰图案 -->
  <circle cx="50" cy="50" r="20" fill="rgba(255,255,255,0.2)"/>
  <circle cx="350" cy="50" r="15" fill="rgba(255,255,255,0.15)"/>
  <circle cx="50" cy="250" r="15" fill="rgba(255,255,255,0.15)"/>
  <circle cx="350" cy="250" r="20" fill="rgba(255,255,255,0.2)"/>
</svg>'''
    
    return svg_template

def convert_svg_to_jpg_fallback(game_name, filename, bg_color):
    """创建简单的HTML文件作为图片替代方案"""
    
    html_content = f'''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <style>
        body {{
            margin: 0;
            padding: 0;
            width: 400px;
            height: 300px;
            background: linear-gradient(135deg, {bg_color} 0%, #000000 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            color: white;
            border: 3px solid white;
            box-sizing: border-box;
        }}
        .game-title {{
            font-size: 28px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            text-align: center;
            margin-bottom: 20px;
        }}
        .label {{
            font-size: 14px;
            opacity: 0.8;
        }}
    </style>
</head>
<body>
    <div class="game-title">{game_name}</div>
    <div class="label">游戏图片</div>
</body>
</html>'''
    
    return html_content

def main():
    """主函数"""
    
    # 确保static目录存在
    if not os.path.exists('static'):
        os.makedirs('static')
    
    # 游戏信息
    games = [
        ("塞尔达传说：旷野之息", "zelda_botw", "#228B22"),      # 森林绿
        ("巫师3：狂猎", "witcher3", "#8B4513"),                # 棕色
        ("艾尔登法环", "elden_ring", "#B8860B"),              # 金色
        ("我的世界", "minecraft", "#228B22"),                  # 绿色
        ("英雄联盟", "lol", "#1E90FF"),                       # 蓝色
        ("荒野大镖客：救赎2", "rdr2", "#A0522D"),              # 棕红色
        ("赛博朋克2077", "cyberpunk2077", "#FF1493"),         # 粉红色
        ("原神", "genshin", "#4682B4"),                       # 钢蓝色
        ("DOTA 2", "dota2", "#DC143C"),                        # 深红色
        ("守望先锋2", "overwatch2", "#FFA500")                 # 橙色
    ]
    
    print("🎨 开始生成游戏占位符图片...")
    
    for game_name, filename, bg_color in games:
        print(f"正在生成: {game_name} -> {filename}")
        
        # 创建SVG图片
        svg_content = create_svg_image(game_name, filename, bg_color)
        svg_filepath = os.path.join('static', f'{filename}.svg')
        
        with open(svg_filepath, 'w', encoding='utf-8') as f:
            f.write(svg_content)
        
        # 创建HTML版本作为备用
        html_content = convert_svg_to_jpg_fallback(game_name, filename, bg_color)
        html_filepath = os.path.join('static', f'{filename}.html')
        
        with open(html_filepath, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ 已保存: {svg_filepath}")
        print(f"✅ 已保存: {html_filepath}")
    
    print("\n🎉 所有占位符图片生成完成！")
    print("\n📁 生成的文件列表:")
    for _, filename, _ in games:
        print(f"   static/{filename}.svg")
        print(f"   static/{filename}.html")
    
    print("\n💡 提示:")
    print("- 生成了SVG格式的占位符图片")
    print("- 您需要将网站代码中的.jpg扩展名改为.svg")
    print("- 或者使用在线工具将SVG转换为JPG格式")
    print("- 建议使用真实的游戏图片替换这些占位符")

if __name__ == "__main__":
    main()
