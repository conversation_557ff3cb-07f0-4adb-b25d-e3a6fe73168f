from flask import Flask, render_template, request, redirect, url_for, flash
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
import os
from datetime import datetime

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///games.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# Initialize database
db = SQLAlchemy(app)

# Initialize login manager
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Define models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(200), nullable=False)
    ratings = db.relationship('Rating', backref='user', lazy=True)
    comments = db.relationship('Comment', backref='user', lazy=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Game(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=False)
    genre = db.Column(db.String(50), nullable=False)
    image_url = db.Column(db.String(200), nullable=True)
    release_date = db.Column(db.String(50), nullable=True)
    developer = db.Column(db.String(100), nullable=True)
    ratings = db.relationship('Rating', backref='game', lazy=True)
    comments = db.relationship('Comment', backref='game', lazy=True)

    @property
    def average_rating(self):
        if not self.ratings:
            return 0
        return sum(rating.score for rating in self.ratings) / len(self.ratings)

class Rating(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    score = db.Column(db.Integer, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    game_id = db.Column(db.Integer, db.ForeignKey('game.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Comment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    content = db.Column(db.Text, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    game_id = db.Column(db.Integer, db.ForeignKey('game.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
def home():
    games = Game.query.all()
    return render_template('home.html', games=games)

@app.route('/game/<int:game_id>')
def game_detail(game_id):
    game = Game.query.get_or_404(game_id)
    return render_template('game_detail.html', game=game)

@app.route('/about')
def about():
    return render_template('about.html')

@app.route('/achievements')
def achievements():
    # 获取网站统计数据
    total_games = Game.query.count()
    total_users = User.query.count()
    total_ratings = Rating.query.count()
    total_comments = Comment.query.count()

    # 获取最高评分游戏
    top_rated_games = Game.query.order_by(Game.average_rating.desc()).limit(3).all()

    # 获取最活跃用户（评分最多的用户）
    from sqlalchemy import func
    most_active_users = db.session.query(
        User.username,
        func.count(Rating.id).label('rating_count')
    ).join(Rating).group_by(User.id).order_by(func.count(Rating.id).desc()).limit(3).all()

    stats = {
        'total_games': total_games,
        'total_users': total_users,
        'total_ratings': total_ratings,
        'total_comments': total_comments,
        'top_rated_games': top_rated_games,
        'most_active_users': most_active_users
    }

    return render_template('achievements.html', stats=stats)

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')

        # Check if username or email already exists
        if User.query.filter_by(username=username).first():
            flash('Username already exists')
            return redirect(url_for('register'))
        if User.query.filter_by(email=email).first():
            flash('Email already exists')
            return redirect(url_for('register'))

        # Create new user
        user = User(username=username, email=email)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()

        flash('Registration successful! Please log in.')
        return redirect(url_for('login'))

    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            login_user(user)
            return redirect(url_for('home'))
        else:
            flash('Invalid username or password')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('home'))

@app.route('/rate/<int:game_id>', methods=['POST'])
@login_required
def rate_game(game_id):
    score = int(request.form.get('rating'))

    # Check if user has already rated this game
    existing_rating = Rating.query.filter_by(user_id=current_user.id, game_id=game_id).first()

    if existing_rating:
        existing_rating.score = score
    else:
        new_rating = Rating(score=score, user_id=current_user.id, game_id=game_id)
        db.session.add(new_rating)

    db.session.commit()
    flash('Rating submitted successfully!')
    return redirect(url_for('game_detail', game_id=game_id))

@app.route('/comment/<int:game_id>', methods=['POST'])
@login_required
def add_comment(game_id):
    content = request.form.get('content')

    if content:
        new_comment = Comment(content=content, user_id=current_user.id, game_id=game_id)
        db.session.add(new_comment)
        db.session.commit()
        flash('Comment added successfully!')

    return redirect(url_for('game_detail', game_id=game_id))

# Initialize database with sample data
def init_db():
    with app.app_context():
        db.create_all()

        # Check if there are already games in the database
        if Game.query.count() == 0:
            # Add sample games
            games = [
                {
                    'title': '塞尔达传说：旷野之息',
                    'description': '《塞尔达传说：旷野之息》是一款开放世界动作冒险游戏，玩家将扮演林克，在一个广阔的海拉尔王国中探索、战斗和解谜。游戏以其创新的物理引擎和自由度高的游戏玩法而闻名。',
                    'genre': '动作冒险',
                    'image_url': 'static/images/zelda.jpg',
                    'release_date': '2017-03-03',
                    'developer': '任天堂'
                },
                {
                    'title': '巫师3：狂猎',
                    'description': '《巫师3：狂猎》是一款角色扮演游戏，玩家将扮演杰洛特，一位猎魔人，在一个充满战争和怪物的世界中寻找自己的养女。游戏以其丰富的剧情、精美的画面和复杂的道德选择系统而著称。',
                    'genre': '角色扮演',
                    'image_url': 'static/images/witcher3.jpg',
                    'release_date': '2015-05-19',
                    'developer': 'CD Projekt Red'
                },
                {
                    'title': '艾尔登法环',
                    'description': '《艾尔登法环》是一款由FromSoftware开发的动作角色扮演游戏，以其开放世界设计和高难度的战斗系统而闻名。玩家将在一个被称为"间界"的奇幻世界中探索，与各种强大的敌人战斗。',
                    'genre': '动作角色扮演',
                    'image_url': 'static/images/eldenring.jpg',
                    'release_date': '2022-02-25',
                    'developer': 'FromSoftware'
                },
                {
                    'title': '我的世界',
                    'description': '《我的世界》是一款沙盒游戏，玩家可以在一个由方块组成的3D世界中探索、采集资源、制作工具和建筑物。游戏没有特定的目标，玩家可以自由地创造和探索。',
                    'genre': '沙盒',
                    'image_url': 'static/images/minecraft.jpg',
                    'release_date': '2011-11-18',
                    'developer': 'Mojang Studios'
                },
                {
                    'title': '英雄联盟',
                    'description': '《英雄联盟》是一款多人在线战术竞技游戏，玩家将扮演一名"英雄"，与队友合作击败敌方团队。游戏以其竞技性和团队合作精神而闻名。',
                    'genre': 'MOBA',
                    'image_url': 'static/images/lol.jpg',
                    'release_date': '2009-10-27',
                    'developer': 'Riot Games'
                },
                {
                    'title': '荒野大镖客：救赎2',
                    'description': '《荒野大镖客：救赎2》是一款开放世界动作冒险游戏，设定在美国西部的末日。玩家将扮演亚瑟·摩根，一名范德林德帮派的成员，在一个正在现代化的世界中生存。',
                    'genre': '动作冒险',
                    'image_url': 'static/images/rdr2.jpg',
                    'release_date': '2018-10-26',
                    'developer': 'Rockstar Games'
                }
            ]

            for game_data in games:
                game = Game(**game_data)
                db.session.add(game)

            db.session.commit()

if __name__ == '__main__':
    # Create directories if they don't exist
    os.makedirs('static/images', exist_ok=True)

    # Initialize database
    init_db()

    # Run the app
    app.run(debug=True)
