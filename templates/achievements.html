{% extends "base.html" %}

{% block title %}成就 - 游戏评分网站{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <!-- 页面标题 -->
        <div class="text-center mb-5">
            <h1 class="display-4 mb-4">
                <i class="fas fa-trophy text-warning me-3"></i>
                网站成就
            </h1>
            <p class="lead text-muted">见证我们的成长历程与重要里程碑</p>
        </div>

        <!-- 统计数据概览 -->
        <div class="row mb-5">
            <div class="col-md-3 mb-4">
                <div class="card text-center h-100 border-primary">
                    <div class="card-body">
                        <i class="fas fa-gamepad fa-3x text-primary mb-3"></i>
                        <h2 class="card-title text-primary">{{ stats.total_games }}</h2>
                        <p class="card-text">收录游戏</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card text-center h-100 border-success">
                    <div class="card-body">
                        <i class="fas fa-users fa-3x text-success mb-3"></i>
                        <h2 class="card-title text-success">{{ stats.total_users }}</h2>
                        <p class="card-text">注册用户</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card text-center h-100 border-warning">
                    <div class="card-body">
                        <i class="fas fa-star fa-3x text-warning mb-3"></i>
                        <h2 class="card-title text-warning">{{ stats.total_ratings }}</h2>
                        <p class="card-text">用户评分</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card text-center h-100 border-info">
                    <div class="card-body">
                        <i class="fas fa-comments fa-3x text-info mb-3"></i>
                        <h2 class="card-title text-info">{{ stats.total_comments }}</h2>
                        <p class="card-text">用户评论</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 重要里程碑 -->
        <div class="card mb-5">
            <div class="card-header">
                <h2 class="mb-0">
                    <i class="fas fa-flag-checkered text-success me-2"></i>
                    重要里程碑
                </h2>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="row mb-4">
                        <div class="col-md-2 text-center">
                            <div class="badge bg-primary fs-6 p-2">2025年5月</div>
                        </div>
                        <div class="col-md-10">
                            <div class="card border-primary">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-rocket text-primary me-2"></i>
                                        网站正式上线
                                    </h5>
                                    <p class="card-text">游戏评分网站正式发布，开始为游戏爱好者提供服务。初始版本包含基础的游戏浏览和评分功能。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-2 text-center">
                            <div class="badge bg-success fs-6 p-2">第1周</div>
                        </div>
                        <div class="col-md-10">
                            <div class="card border-success">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-users text-success me-2"></i>
                                        用户注册系统上线
                                    </h5>
                                    <p class="card-text">完善用户注册和登录功能，用户可以创建个人账户，享受个性化的游戏评分体验。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-2 text-center">
                            <div class="badge bg-warning fs-6 p-2">第2周</div>
                        </div>
                        <div class="col-md-10">
                            <div class="card border-warning">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-comments text-warning me-2"></i>
                                        评论功能发布
                                    </h5>
                                    <p class="card-text">新增游戏评论功能，用户不仅可以为游戏评分，还能分享详细的游戏体验和心得。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-2 text-center">
                            <div class="badge bg-info fs-6 p-2">第3周</div>
                        </div>
                        <div class="col-md-10">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-images text-info me-2"></i>
                                        游戏图片系统
                                    </h5>
                                    <p class="card-text">为每款游戏添加高质量的展示图片，提升用户浏览体验，让游戏信息更加直观。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-2 text-center">
                            <div class="badge bg-secondary fs-6 p-2">当前</div>
                        </div>
                        <div class="col-md-10">
                            <div class="card border-secondary">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="fas fa-trophy text-secondary me-2"></i>
                                        成就系统上线
                                    </h5>
                                    <p class="card-text">推出网站成就页面，展示平台发展历程和重要数据，让用户了解我们的成长轨迹。</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 最高评分游戏 -->
        {% if stats.top_rated_games %}
        <div class="card mb-5">
            <div class="card-header">
                <h2 class="mb-0">
                    <i class="fas fa-crown text-warning me-2"></i>
                    最高评分游戏
                </h2>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for game in stats.top_rated_games %}
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            {% if game.image_url %}
                            <img src="{{ game.image_url }}" class="card-img-top" style="height: 200px; object-fit: cover;" alt="{{ game.title }}">
                            {% else %}
                            <div class="card-img-top bg-secondary d-flex align-items-center justify-content-center" style="height: 200px;">
                                <span class="text-white">暂无图片</span>
                            </div>
                            {% endif %}
                            <div class="card-body text-center">
                                <h5 class="card-title">{{ game.title }}</h5>
                                <div class="mb-2">
                                    {% set rating = game.average_rating|round(1) %}
                                    <div class="stars">
                                        {% for i in range(5) %}
                                            {% if i < rating|int %}
                                                <i class="fas fa-star text-warning"></i>
                                            {% elif i == rating|int and rating % 1 >= 0.5 %}
                                                <i class="fas fa-star-half-alt text-warning"></i>
                                            {% else %}
                                                <i class="far fa-star text-warning"></i>
                                            {% endif %}
                                        {% endfor %}
                                        <span class="ms-1">{{ rating }}</span>
                                    </div>
                                </div>
                                <p class="card-text text-muted">{{ game.genre }}</p>
                                <a href="{{ url_for('game_detail', game_id=game.id) }}" class="btn btn-primary btn-sm">查看详情</a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 最活跃用户 -->
        {% if stats.most_active_users %}
        <div class="card mb-5">
            <div class="card-header">
                <h2 class="mb-0">
                    <i class="fas fa-medal text-success me-2"></i>
                    最活跃用户
                </h2>
            </div>
            <div class="card-body">
                <div class="row">
                    {% for user in stats.most_active_users %}
                    <div class="col-md-4 mb-3">
                        <div class="card text-center h-100">
                            <div class="card-body">
                                <i class="fas fa-user-circle fa-3x text-primary mb-3"></i>
                                <h5 class="card-title">{{ user.username }}</h5>
                                <p class="card-text">
                                    <span class="badge bg-success fs-6">{{ user.rating_count }} 次评分</span>
                                </p>
                                <p class="card-text text-muted">感谢您的积极参与！</p>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 未来规划 -->
        <div class="card">
            <div class="card-header">
                <h2 class="mb-0">
                    <i class="fas fa-road text-info me-2"></i>
                    未来规划
                </h2>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <div class="card border-primary h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-mobile-alt text-primary me-2"></i>
                                    移动端优化
                                </h5>
                                <p class="card-text">开发移动端应用，让用户随时随地浏览和评价游戏。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-success h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-globe text-success me-2"></i>
                                    多语言支持
                                </h5>
                                <p class="card-text">支持多种语言，为全球用户提供本地化服务。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-warning h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-robot text-warning me-2"></i>
                                    智能推荐
                                </h5>
                                <p class="card-text">基于用户偏好和评分历史，提供个性化游戏推荐。</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="card border-info h-100">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="fas fa-users text-info me-2"></i>
                                    社区功能
                                </h5>
                                <p class="card-text">建立游戏社区，让玩家可以互相交流和分享游戏心得。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
