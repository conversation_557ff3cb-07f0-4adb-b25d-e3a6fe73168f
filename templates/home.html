{% extends "base.html" %}

{% block title %}热门游戏 - 游戏评分网站{% endblock %}

{% block content %}
<!-- 网站简介 -->
<div class="jumbotron bg-primary text-white rounded mb-5 p-5">
    <div class="container text-center">
        <h1 class="display-4 mb-3">
            <i class="fas fa-gamepad me-3"></i>
            欢迎来到游戏评分网站
        </h1>
        <p class="lead mb-4">专业的游戏评分与评论平台，为游戏爱好者提供最真实的游戏体验分享</p>
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="row text-center">
                    <div class="col-md-4 mb-3">
                        <i class="fas fa-star fa-2x mb-2"></i>
                        <h5>专业评分</h5>
                        <p>真实用户评分，公正可信</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <i class="fas fa-comments fa-2x mb-2"></i>
                        <h5>互动评论</h5>
                        <p>分享游戏心得，交流游戏体验</p>
                    </div>
                    <div class="col-md-4 mb-3">
                        <i class="fas fa-search fa-2x mb-2"></i>
                        <h5>精选游戏</h5>
                        <p>精心收录热门游戏，发现优质作品</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-4">
            <a href="{{ url_for('about') }}" class="btn btn-light btn-lg me-3">
                <i class="fas fa-info-circle me-2"></i>了解更多
            </a>
            <a href="{{ url_for('achievements') }}" class="btn btn-outline-light btn-lg">
                <i class="fas fa-trophy me-2"></i>查看成就
            </a>
        </div>
    </div>
</div>

<h1 class="mb-4">热门游戏</h1>

<div class="row">
    {% for game in games %}
    <div class="col-md-4 mb-4">
        <div class="card h-100">
            {% if game.image_url %}
            <img src="{{ game.image_url }}" class="card-img-top game-card-img" alt="{{ game.title }}">
            {% else %}
            <div class="card-img-top game-card-img bg-secondary d-flex align-items-center justify-content-center">
                <span class="text-white">暂无图片</span>
            </div>
            {% endif %}
            <div class="card-body">
                <h5 class="card-title">{{ game.title }}</h5>
                <p class="card-text text-muted">{{ game.genre }}</p>
                <div class="mb-2">
                    {% set rating = game.average_rating|round(1) %}
                    <div class="stars">
                        {% for i in range(5) %}
                            {% if i < rating|int %}
                                <i class="fas fa-star text-warning"></i>
                            {% elif i == rating|int and rating % 1 >= 0.5 %}
                                <i class="fas fa-star-half-alt text-warning"></i>
                            {% else %}
                                <i class="far fa-star text-warning"></i>
                            {% endif %}
                        {% endfor %}
                        <span class="ms-1">{{ rating }}</span>
                    </div>
                </div>
                <p class="card-text">{{ game.description|truncate(100) }}</p>
                <a href="{{ url_for('game_detail', game_id=game.id) }}" class="btn btn-primary">查看详情</a>
            </div>
            <div class="card-footer text-muted">
                <small>开发商: {{ game.developer }}</small>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% endblock %}
