{% extends "base.html" %}

{% block title %}{{ game.title }} - 游戏评分网站{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-4">
        {% if game.image_url %}
        <img src="{{ game.image_url }}" class="img-fluid rounded" alt="{{ game.title }}">
        {% else %}
        <div class="bg-secondary rounded d-flex align-items-center justify-content-center" style="height: 300px;">
            <span class="text-white">暂无图片</span>
        </div>
        {% endif %}
    </div>
    <div class="col-md-8">
        <h1>{{ game.title }}</h1>
        <div class="mb-3">
            {% set rating = game.average_rating|round(1) %}
            <div class="stars">
                {% for i in range(5) %}
                    {% if i < rating|int %}
                        <i class="fas fa-star text-warning"></i>
                    {% elif i == rating|int and rating % 1 >= 0.5 %}
                        <i class="fas fa-star-half-alt text-warning"></i>
                    {% else %}
                        <i class="far fa-star text-warning"></i>
                    {% endif %}
                {% endfor %}
                <span class="ms-1">{{ rating }} ({{ game.ratings|length }} 评分)</span>
            </div>
        </div>
        <p><strong>类型:</strong> {{ game.genre }}</p>
        <p><strong>开发商:</strong> {{ game.developer }}</p>
        <p><strong>发行日期:</strong> {{ game.release_date }}</p>
        <div class="mt-4">
            <h4>游戏介绍</h4>
            <p>{{ game.description }}</p>
        </div>
    </div>
</div>

<div class="row mt-5">
    <div class="col-md-6">
        <h3>评分</h3>
        {% if current_user.is_authenticated %}
        <form action="{{ url_for('rate_game', game_id=game.id) }}" method="post" class="mb-4">
            <div class="mb-3">
                <label for="rating" class="form-label">您的评分</label>
                <select class="form-select" id="rating" name="rating" required>
                    <option value="" selected disabled>选择评分</option>
                    <option value="1">1 - 很差</option>
                    <option value="2">2 - 较差</option>
                    <option value="3">3 - 一般</option>
                    <option value="4">4 - 不错</option>
                    <option value="5">5 - 很好</option>
                </select>
            </div>
            <button type="submit" class="btn btn-primary">提交评分</button>
        </form>
        {% else %}
        <div class="alert alert-info">
            请<a href="{{ url_for('login') }}">登录</a>后进行评分
        </div>
        {% endif %}

        <h4>评分分布</h4>
        <div class="rating-distribution">
            {% for i in range(5, 0, -1) %}
            {% set count = game.ratings|selectattr('score', 'eq', i)|list|length %}
            {% set percentage = (count / game.ratings|length * 100)|round if game.ratings|length > 0 else 0 %}
            <div class="d-flex align-items-center mb-1">
                <div class="me-2">{{ i }} <i class="fas fa-star text-warning"></i></div>
                <div class="progress flex-grow-1" style="height: 15px;">
                    <div class="progress-bar bg-warning" role="progressbar" style="width: {{ percentage }}%;" aria-valuenow="{{ percentage }}" aria-valuemin="0" aria-valuemax="100">{{ percentage }}%</div>
                </div>
                <div class="ms-2">{{ count }}</div>
            </div>
            {% endfor %}
        </div>
    </div>

    <div class="col-md-6">
        <h3>评论 ({{ game.comments|length }})</h3>
        {% if current_user.is_authenticated %}
        <form action="{{ url_for('add_comment', game_id=game.id) }}" method="post" class="mb-4">
            <div class="mb-3">
                <label for="content" class="form-label">添加评论</label>
                <textarea class="form-control" id="content" name="content" rows="3" required></textarea>
            </div>
            <button type="submit" class="btn btn-primary">提交评论</button>
        </form>
        {% else %}
        <div class="alert alert-info">
            请<a href="{{ url_for('login') }}">登录</a>后发表评论
        </div>
        {% endif %}

        <div class="comments-list">
            {% if game.comments %}
                {% for comment in game.comments|sort(attribute='created_at', reverse=true) %}
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">{{ comment.user.username }}</h5>
                        <h6 class="card-subtitle mb-2 text-muted">{{ comment.created_at.strftime('%Y-%m-%d %H:%M') }}</h6>
                        <p class="card-text">{{ comment.content }}</p>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="alert alert-secondary">
                    暂无评论，成为第一个评论的人吧！
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
