#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏图片占位符生成器
为游戏网站生成占位符图片
"""

import os
from PIL import Image, ImageDraw, ImageFont
import textwrap

def create_placeholder_image(game_name, filename, size=(400, 300), bg_color=(64, 128, 255)):
    """创建游戏占位符图片"""
    
    # 创建图片
    img = Image.new('RGB', size, bg_color)
    draw = ImageDraw.Draw(img)
    
    # 尝试使用系统字体
    try:
        # Windows系统字体
        font_large = ImageFont.truetype("msyh.ttc", 32)  # 微软雅黑
        font_small = ImageFont.truetype("msyh.ttc", 16)
    except:
        try:
            font_large = ImageFont.truetype("arial.ttf", 32)
            font_small = ImageFont.truetype("arial.ttf", 16)
        except:
            # 使用默认字体
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
    
    # 绘制游戏名称
    text_lines = textwrap.wrap(game_name, width=15)
    
    # 计算文本位置
    total_height = len(text_lines) * 40
    start_y = (size[1] - total_height) // 2
    
    for i, line in enumerate(text_lines):
        # 获取文本尺寸
        bbox = draw.textbbox((0, 0), line, font=font_large)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # 居中绘制
        x = (size[0] - text_width) // 2
        y = start_y + i * 40
        
        # 绘制阴影
        draw.text((x+2, y+2), line, font=font_large, fill=(0, 0, 0, 128))
        # 绘制文本
        draw.text((x, y), line, font=font_large, fill=(255, 255, 255))
    
    # 绘制装饰边框
    draw.rectangle([10, 10, size[0]-10, size[1]-10], outline=(255, 255, 255), width=3)
    
    # 在底部添加"游戏图片"标识
    label = "游戏图片"
    bbox = draw.textbbox((0, 0), label, font=font_small)
    label_width = bbox[2] - bbox[0]
    draw.text(((size[0] - label_width) // 2, size[1] - 30), label, 
              font=font_small, fill=(255, 255, 255, 200))
    
    return img

def main():
    """主函数"""
    
    # 确保static目录存在
    if not os.path.exists('static'):
        os.makedirs('static')
    
    # 游戏信息
    games = [
        ("塞尔达传说：旷野之息", "zelda_botw.jpg", (34, 139, 34)),      # 森林绿
        ("巫师3：狂猎", "witcher3.jpg", (139, 69, 19)),                # 棕色
        ("艾尔登法环", "elden_ring.jpg", (184, 134, 11)),              # 金色
        ("我的世界", "minecraft.jpg", (34, 139, 34)),                  # 绿色
        ("英雄联盟", "lol.jpg", (30, 144, 255)),                       # 蓝色
        ("荒野大镖客：救赎2", "rdr2.jpg", (160, 82, 45)),              # 棕红色
        ("赛博朋克2077", "cyberpunk2077.jpg", (255, 20, 147)),         # 粉红色
        ("原神", "genshin.jpg", (70, 130, 180)),                       # 钢蓝色
        ("DOTA 2", "dota2.jpg", (220, 20, 60)),                        # 深红色
        ("守望先锋2", "overwatch2.jpg", (255, 165, 0))                 # 橙色
    ]
    
    print("🎨 开始生成游戏占位符图片...")
    
    for game_name, filename, bg_color in games:
        print(f"正在生成: {game_name} -> {filename}")
        
        # 创建图片
        img = create_placeholder_image(game_name, filename, bg_color=bg_color)
        
        # 保存图片
        filepath = os.path.join('static', filename)
        img.save(filepath, 'JPEG', quality=85)
        
        print(f"✅ 已保存: {filepath}")
    
    print("\n🎉 所有占位符图片生成完成！")
    print("\n📁 生成的文件列表:")
    for _, filename, _ in games:
        print(f"   static/{filename}")
    
    print("\n💡 提示:")
    print("- 这些是占位符图片，您可以用真实的游戏图片替换它们")
    print("- 建议图片尺寸为 400x300 像素")
    print("- 支持 JPG、PNG 等常见格式")
    print("- 现在可以访问 http://localhost:8081/ 查看效果")

if __name__ == "__main__":
    main()
