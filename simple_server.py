import http.server
import socketserver
import json
import os
import sqlite3
import urllib.parse
from datetime import datetime
from html import escape

# 创建数据目录
os.makedirs('static/images', exist_ok=True)

# 创建数据库
conn = sqlite3.connect('games.db')
cursor = conn.cursor()

# 创建表
cursor.execute('''
CREATE TABLE IF NOT EXISTS games (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    genre TEXT NOT NULL,
    image_url TEXT,
    release_date TEXT,
    developer TEXT
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS ratings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    score INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    game_id INTEGER NOT NULL,
    created_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (game_id) REFERENCES games (id)
)
''')

cursor.execute('''
CREATE TABLE IF NOT EXISTS comments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    content TEXT NOT NULL,
    user_id INTEGER NOT NULL,
    game_id INTEGER NOT NULL,
    created_at TEXT NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIGN KEY (game_id) REFERENCES games (id)
)
''')

# 添加示例游戏数据
sample_games = [
    {
        'title': '塞尔达传说：旷野之息',
        'description': '《塞尔达传说：旷野之息》是一款开放世界动作冒险游戏，玩家将扮演林克，在一个广阔的海拉尔王国中探索、战斗和解谜。游戏以其创新的物理引擎和自由度高的游戏玩法而闻名。',
        'genre': '动作冒险',
        'image_url': '/static/images/game1.svg',
        'release_date': '2017-03-03',
        'developer': '任天堂'
    },
    {
        'title': '巫师3：狂猎',
        'description': '《巫师3：狂猎》是一款角色扮演游戏，玩家将扮演杰洛特，一位猎魔人，在一个充满战争和怪物的世界中寻找自己的养女。游戏以其丰富的剧情、精美的画面和复杂的道德选择系统而著称。',
        'genre': '角色扮演',
        'image_url': '/static/images/game2.svg',
        'release_date': '2015-05-19',
        'developer': 'CD Projekt Red'
    },
    {
        'title': '艾尔登法环',
        'description': '《艾尔登法环》是一款由FromSoftware开发的动作角色扮演游戏，以其开放世界设计和高难度的战斗系统而闻名。玩家将在一个被称为"间界"的奇幻世界中探索，与各种强大的敌人战斗。',
        'genre': '动作角色扮演',
        'image_url': '/static/images/game3.svg',
        'release_date': '2022-02-25',
        'developer': 'FromSoftware'
    },
    {
        'title': '我的世界',
        'description': '《我的世界》是一款沙盒游戏，玩家可以在一个由方块组成的3D世界中探索、采集资源、制作工具和建筑物。游戏没有特定的目标，玩家可以自由地创造和探索。',
        'genre': '沙盒',
        'image_url': '/static/images/game4.svg',
        'release_date': '2011-11-18',
        'developer': 'Mojang Studios'
    },
    {
        'title': '英雄联盟',
        'description': '《英雄联盟》是一款多人在线战术竞技游戏，玩家将扮演一名"英雄"，与队友合作击败敌方团队。游戏以其竞技性和团队合作精神而闻名。',
        'genre': 'MOBA',
        'image_url': '/static/images/game5.svg',
        'release_date': '2009-10-27',
        'developer': 'Riot Games'
    },
    {
        'title': '荒野大镖客：救赎2',
        'description': '《荒野大镖客：救赎2》是一款开放世界动作冒险游戏，设定在美国西部的末日。玩家将扮演亚瑟·摩根，一名范德林德帮派的成员，在一个正在现代化的世界中生存。',
        'genre': '动作冒险',
        'image_url': '/static/images/game6.svg',
        'release_date': '2018-10-26',
        'developer': 'Rockstar Games'
    }
]

# 检查是否已有游戏数据
cursor.execute("SELECT COUNT(*) FROM games")
game_count = cursor.fetchone()[0]

if game_count == 0:
    for game in sample_games:
        cursor.execute(
            "INSERT INTO games (title, description, genre, image_url, release_date, developer) VALUES (?, ?, ?, ?, ?, ?)",
            (game['title'], game['description'], game['genre'], game['image_url'], game['release_date'], game['developer'])
        )

conn.commit()
conn.close()

# HTML 模板
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }}
        .navbar {{
            background-color: #343a40;
            color: white;
            padding: 1rem;
        }}
        .navbar a {{
            color: white;
            text-decoration: none;
            margin-right: 1rem;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
        }}
        .game-list {{
            margin-top: 2rem;
        }}
        .game-item {{
            display: flex;
            margin-bottom: 2rem;
            background-color: white;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .game-image {{
            flex: 0 0 200px;
        }}
        .game-image img {{
            width: 100%;
            height: 200px;
            object-fit: cover;
        }}
        .game-info {{
            flex: 1;
            padding: 1rem;
        }}
        .game-title {{
            font-size: 1.5rem;
            margin-top: 0;
            margin-bottom: 0.5rem;
        }}
        .game-genre {{
            color: #6c757d;
            margin-bottom: 0.5rem;
        }}
        .game-description {{
            margin-bottom: 1rem;
        }}
        .btn {{
            display: inline-block;
            background-color: #007bff;
            color: white;
            padding: 0.5rem 1rem;
            text-decoration: none;
            border-radius: 3px;
        }}
        .game-detail {{
            display: flex;
            flex-wrap: wrap;
            margin-top: 2rem;
        }}
        .game-detail-image {{
            flex: 0 0 300px;
            margin-right: 2rem;
            margin-bottom: 1rem;
        }}
        .game-detail-image img {{
            width: 100%;
            border-radius: 5px;
        }}
        .game-detail-info {{
            flex: 1;
            min-width: 300px;
        }}
        .rating-form, .comment-form {{
            margin-top: 2rem;
            background-color: white;
            padding: 1rem;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .form-group {{
            margin-bottom: 1rem;
        }}
        .form-group label {{
            display: block;
            margin-bottom: 0.5rem;
        }}
        .form-control {{
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 3px;
        }}
        .comments-list {{
            margin-top: 2rem;
        }}
        .comment-card {{
            background-color: white;
            padding: 1rem;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
        }}
        .comment-header {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }}
        .comment-username {{
            font-weight: bold;
        }}
        .comment-date {{
            color: #6c757d;
        }}
        footer {{
            background-color: #343a40;
            color: white;
            text-align: center;
            padding: 1rem;
            margin-top: 3rem;
        }}
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="container">
            <a href="/" style="font-size: 1.5rem;">游戏评分网站</a>
            <div>
                <a href="/">首页</a>
                <a href="/about">关于我们</a>
                <a href="/achievements">成就</a>
                <a href="/login">登录</a>
                <a href="/register">注册</a>
            </div>
        </div>
    </nav>

    <div class="container">
        {content}
    </div>

    <footer>
        <div class="container">
            <p>&copy; 2025 游戏评分网站. 保留所有权利.</p>
        </div>
    </footer>
</body>
</html>
'''

# 首页内容模板
HOME_TEMPLATE = '''
<!-- 网站简介 -->
<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 3rem 2rem; border-radius: 10px; margin-bottom: 2rem; text-align: center;">
    <h1 style="font-size: 2.5rem; margin-bottom: 1rem;">
        🎮 欢迎来到游戏评分网站
    </h1>
    <p style="font-size: 1.2rem; margin-bottom: 2rem; opacity: 0.9;">专业的游戏评分与评论平台，为游戏爱好者提供最真实的游戏体验分享</p>
    <div style="display: flex; justify-content: center; gap: 2rem; flex-wrap: wrap; margin-bottom: 2rem;">
        <div style="text-align: center;">
            ⭐ <strong>专业评分</strong><br>
            <small>真实用户评分，公正可信</small>
        </div>
        <div style="text-align: center;">
            💬 <strong>互动评论</strong><br>
            <small>分享游戏心得，交流游戏体验</small>
        </div>
        <div style="text-align: center;">
            🔍 <strong>精选游戏</strong><br>
            <small>精心收录热门游戏，发现优质作品</small>
        </div>
    </div>
    <div>
        <a href="/about" style="background: rgba(255,255,255,0.2); color: white; padding: 0.8rem 1.5rem; text-decoration: none; border-radius: 5px; margin: 0 0.5rem; display: inline-block;">
            📝 了解更多
        </a>
        <a href="/achievements" style="background: rgba(255,255,255,0.1); color: white; padding: 0.8rem 1.5rem; text-decoration: none; border-radius: 5px; margin: 0 0.5rem; display: inline-block; border: 1px solid rgba(255,255,255,0.3);">
            🏆 查看成就
        </a>
    </div>
</div>

<h1>热门游戏</h1>
<div class="game-list">
    {game_cards}
</div>
'''

# 游戏卡片模板
GAME_CARD_TEMPLATE = '''
<div class="game-item">
    <div class="game-image">
        <img src="{image_url}" alt="{title}">
    </div>
    <div class="game-info">
        <h3 class="game-title">{title}</h3>
        <p class="game-genre">{genre}</p>
        <p class="game-description">{description}</p>
        <a href="/game/{id}" class="btn">查看详情</a>
    </div>
</div>
'''

# 游戏详情页模板
GAME_DETAIL_TEMPLATE = '''
<div class="game-detail">
    <div class="game-detail-image">
        <img src="{image_url}" alt="{title}">
    </div>
    <div class="game-detail-info">
        <h1>{title}</h1>
        <p><strong>类型:</strong> {genre}</p>
        <p><strong>开发商:</strong> {developer}</p>
        <p><strong>发行日期:</strong> {release_date}</p>
        <h3>游戏介绍</h3>
        <p>{description}</p>
    </div>
</div>

<div class="rating-form">
    <h3>评分</h3>
    <form action="/rate/{id}" method="post">
        <div class="form-group">
            <label for="rating">您的评分</label>
            <select class="form-control" id="rating" name="rating" required>
                <option value="" selected disabled>选择评分</option>
                <option value="1">1 - 很差</option>
                <option value="2">2 - 较差</option>
                <option value="3">3 - 一般</option>
                <option value="4">4 - 不错</option>
                <option value="5">5 - 很好</option>
            </select>
        </div>
        <button type="submit" class="btn">提交评分</button>
    </form>
</div>

<div class="comment-form">
    <h3>评论</h3>
    <form action="/comment/{id}" method="post">
        <div class="form-group">
            <label for="content">添加评论</label>
            <textarea class="form-control" id="content" name="content" rows="3" required></textarea>
        </div>
        <button type="submit" class="btn">提交评论</button>
    </form>
</div>

<div class="comments-list">
    <h3>所有评论</h3>
    {comments}
</div>
'''

# 评论卡片模板
COMMENT_TEMPLATE = '''
<div class="comment-card">
    <div class="comment-header">
        <span class="comment-username">{username}</span>
        <span class="comment-date">{date}</span>
    </div>
    <p>{content}</p>
</div>
'''

# 关于我们页面模板
ABOUT_TEMPLATE = '''
<div style="max-width: 800px; margin: 0 auto;">
    <div style="text-align: center; margin-bottom: 3rem;">
        <h1 style="font-size: 2.5rem; margin-bottom: 1rem; color: #333;">关于游戏评分网站</h1>
        <p style="font-size: 1.2rem; color: #666;">专业的游戏评分与评论平台，为游戏爱好者提供最真实的游戏体验分享</p>
    </div>

    <div style="background: #f8f9fa; padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
        <h2 style="color: #007bff; margin-bottom: 1rem;">🎮 网站简介</h2>
        <p>游戏评分网站是一个专门为游戏爱好者打造的综合性平台。我们致力于为用户提供最全面、最客观的游戏信息和评价体系。</p>
        <p>无论您是资深玩家还是游戏新手，都能在这里找到适合自己的游戏，并与其他玩家分享游戏体验。</p>
    </div>

    <div style="display: flex; gap: 1rem; margin-bottom: 2rem; flex-wrap: wrap;">
        <div style="flex: 1; background: #e8f5e8; padding: 1.5rem; border-radius: 8px; min-width: 250px;">
            <h3 style="color: #28a745; margin-bottom: 1rem;">🎯 我们的使命</h3>
            <p>为全球游戏爱好者提供一个公正、透明、易用的游戏评价平台，帮助玩家发现优质游戏。</p>
        </div>
        <div style="flex: 1; background: #e3f2fd; padding: 1.5rem; border-radius: 8px; min-width: 250px;">
            <h3 style="color: #2196f3; margin-bottom: 1rem;">👁️ 我们的愿景</h3>
            <p>成为全球最受信赖的游戏评分平台，构建一个活跃的游戏社区。</p>
        </div>
    </div>

    <div style="background: #fff3cd; padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
        <h2 style="color: #856404; margin-bottom: 1rem;">⭐ 主要功能</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <div>🔍 <strong>游戏浏览：</strong>浏览各种类型的热门游戏</div>
            <div>⭐ <strong>评分系统：</strong>为喜爱的游戏打分（1-5星）</div>
            <div>💬 <strong>评论功能：</strong>分享游戏体验和心得</div>
            <div>👤 <strong>用户系统：</strong>注册登录，个性化体验</div>
            <div>📊 <strong>统计分析：</strong>查看游戏评分分布和趋势</div>
            <div>🏆 <strong>成就系统：</strong>记录平台发展里程碑</div>
        </div>
    </div>

    <div style="background: #f8f9fa; padding: 2rem; border-radius: 10px; text-align: center;">
        <h2 style="color: #6c757d; margin-bottom: 1rem;">📧 联系我们</h2>
        <p>如果您有任何建议、意见或合作意向，欢迎随时与我们联系。</p>
        <p><strong>邮箱：</strong><EMAIL> | <strong>GitHub：</strong>github.com/gamerating</p>
    </div>
</div>
'''

# 成就页面模板
ACHIEVEMENTS_TEMPLATE = '''
<div style="max-width: 1000px; margin: 0 auto;">
    <div style="text-align: center; margin-bottom: 3rem;">
        <h1 style="font-size: 2.5rem; margin-bottom: 1rem; color: #333;">🏆 网站成就</h1>
        <p style="font-size: 1.2rem; color: #666;">见证我们的成长历程与重要里程碑</p>
    </div>

    <!-- 统计数据 -->
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-bottom: 3rem;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 10px; text-align: center;">
            <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">{total_games}</div>
            <div>🎮 收录游戏</div>
        </div>
        <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 2rem; border-radius: 10px; text-align: center;">
            <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">{total_users}</div>
            <div>👥 注册用户</div>
        </div>
        <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 2rem; border-radius: 10px; text-align: center;">
            <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">{total_ratings}</div>
            <div>⭐ 用户评分</div>
        </div>
        <div style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white; padding: 2rem; border-radius: 10px; text-align: center;">
            <div style="font-size: 2.5rem; font-weight: bold; margin-bottom: 0.5rem;">{total_comments}</div>
            <div>💬 用户评论</div>
        </div>
    </div>

    <!-- 重要里程碑 -->
    <div style="background: #f8f9fa; padding: 2rem; border-radius: 10px; margin-bottom: 2rem;">
        <h2 style="color: #28a745; margin-bottom: 2rem;">🏁 重要里程碑</h2>
        <div style="border-left: 4px solid #007bff; padding-left: 1rem;">
            <div style="margin-bottom: 2rem;">
                <div style="background: #007bff; color: white; padding: 0.5rem 1rem; border-radius: 20px; display: inline-block; margin-bottom: 1rem;">2025年5月</div>
                <h4 style="color: #007bff; margin-bottom: 0.5rem;">🚀 网站正式上线</h4>
                <p>游戏评分网站正式发布，开始为游戏爱好者提供服务。</p>
            </div>
            <div style="margin-bottom: 2rem;">
                <div style="background: #28a745; color: white; padding: 0.5rem 1rem; border-radius: 20px; display: inline-block; margin-bottom: 1rem;">第1周</div>
                <h4 style="color: #28a745; margin-bottom: 0.5rem;">👥 用户注册系统上线</h4>
                <p>完善用户注册和登录功能，用户可以创建个人账户。</p>
            </div>
            <div style="margin-bottom: 2rem;">
                <div style="background: #ffc107; color: white; padding: 0.5rem 1rem; border-radius: 20px; display: inline-block; margin-bottom: 1rem;">第2周</div>
                <h4 style="color: #ffc107; margin-bottom: 0.5rem;">💬 评论功能发布</h4>
                <p>新增游戏评论功能，用户不仅可以为游戏评分，还能分享详细的游戏体验。</p>
            </div>
            <div style="margin-bottom: 2rem;">
                <div style="background: #17a2b8; color: white; padding: 0.5rem 1rem; border-radius: 20px; display: inline-block; margin-bottom: 1rem;">第3周</div>
                <h4 style="color: #17a2b8; margin-bottom: 0.5rem;">🖼️ 游戏图片系统</h4>
                <p>为每款游戏添加高质量的展示图片，提升用户浏览体验。</p>
            </div>
            <div>
                <div style="background: #6c757d; color: white; padding: 0.5rem 1rem; border-radius: 20px; display: inline-block; margin-bottom: 1rem;">当前</div>
                <h4 style="color: #6c757d; margin-bottom: 0.5rem;">🏆 成就系统上线</h4>
                <p>推出网站成就页面，展示平台发展历程和重要数据。</p>
            </div>
        </div>
    </div>

    <!-- 未来规划 -->
    <div style="background: #e3f2fd; padding: 2rem; border-radius: 10px;">
        <h2 style="color: #1976d2; margin-bottom: 2rem;">🛣️ 未来规划</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
            <div style="background: white; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #007bff;">
                <h4 style="color: #007bff; margin-bottom: 1rem;">📱 移动端优化</h4>
                <p>开发移动端应用，让用户随时随地浏览和评价游戏。</p>
            </div>
            <div style="background: white; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #28a745;">
                <h4 style="color: #28a745; margin-bottom: 1rem;">🌍 多语言支持</h4>
                <p>支持多种语言，为全球用户提供本地化服务。</p>
            </div>
            <div style="background: white; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #ffc107;">
                <h4 style="color: #ffc107; margin-bottom: 1rem;">🤖 智能推荐</h4>
                <p>基于用户偏好和评分历史，提供个性化游戏推荐。</p>
            </div>
            <div style="background: white; padding: 1.5rem; border-radius: 8px; border-left: 4px solid #17a2b8;">
                <h4 style="color: #17a2b8; margin-bottom: 1rem;">👥 社区功能</h4>
                <p>建立游戏社区，让玩家可以互相交流和分享游戏心得。</p>
            </div>
        </div>
    </div>
</div>
'''

# 登录页面模板
LOGIN_TEMPLATE = '''
<h1>登录</h1>
<div class="rating-form">
    <form action="/login" method="post">
        <div class="form-group">
            <label for="username">用户名</label>
            <input type="text" class="form-control" id="username" name="username" required>
        </div>
        <div class="form-group">
            <label for="password">密码</label>
            <input type="password" class="form-control" id="password" name="password" required>
        </div>
        <button type="submit" class="btn">登录</button>
    </form>
    <p style="margin-top: 1rem;">还没有账号? <a href="/register">注册</a></p>
</div>
'''

# 注册页面模板
REGISTER_TEMPLATE = '''
<h1>注册</h1>
<div class="rating-form">
    <form action="/register" method="post">
        <div class="form-group">
            <label for="username">用户名</label>
            <input type="text" class="form-control" id="username" name="username" required>
        </div>
        <div class="form-group">
            <label for="password">密码</label>
            <input type="password" class="form-control" id="password" name="password" required>
        </div>
        <button type="submit" class="btn">注册</button>
    </form>
    <p style="margin-top: 1rem;">已有账号? <a href="/login">登录</a></p>
</div>
'''

class GameHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        # 解析URL
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path

        # 处理静态文件
        if path.startswith('/static/'):
            return super().do_GET()

        # 处理首页
        if path == '/' or path == '/index.html':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()

            # 获取所有游戏
            conn = sqlite3.connect('games.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM games")
            games = cursor.fetchall()
            conn.close()

            # 生成游戏卡片
            game_cards = ""
            for game in games:
                description = game['description']
                if len(description) > 100:
                    description = description[:100] + "..."

                game_cards += GAME_CARD_TEMPLATE.format(
                    id=game['id'],
                    title=game['title'],
                    genre=game['genre'],
                    description=description,
                    image_url=game['image_url']
                )

            # 生成首页内容
            content = HOME_TEMPLATE.format(game_cards=game_cards)

            # 生成完整HTML
            html = HTML_TEMPLATE.format(title="热门游戏 - 游戏评分网站", content=content)
            self.wfile.write(html.encode('utf-8'))
            return

        # 处理游戏详情页
        if path.startswith('/game/'):
            game_id = path.split('/')[-1]
            if game_id.isdigit():
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()

                # 获取游戏信息
                conn = sqlite3.connect('games.db')
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM games WHERE id = ?", (game_id,))
                game = cursor.fetchone()

                if game:
                    # 获取评论
                    cursor.execute("""
                        SELECT c.*, u.username
                        FROM comments c
                        JOIN users u ON c.user_id = u.id
                        WHERE c.game_id = ?
                        ORDER BY c.created_at DESC
                    """, (game_id,))
                    comments_data = cursor.fetchall()

                    # 生成评论HTML
                    comments_html = ""
                    if comments_data:
                        for comment in comments_data:
                            comments_html += COMMENT_TEMPLATE.format(
                                username=comment['username'],
                                date=comment['created_at'],
                                content=comment['content']
                            )
                    else:
                        comments_html = "<p>暂无评论，成为第一个评论的人吧！</p>"

                    # 生成游戏详情内容
                    content = GAME_DETAIL_TEMPLATE.format(
                        id=game['id'],
                        title=game['title'],
                        genre=game['genre'],
                        developer=game['developer'],
                        release_date=game['release_date'],
                        description=game['description'],
                        image_url=game['image_url'],
                        comments=comments_html
                    )

                    # 生成完整HTML
                    html = HTML_TEMPLATE.format(title=f"{game['title']} - 游戏评分网站", content=content)
                    self.wfile.write(html.encode('utf-8'))
                else:
                    self.send_error(404, "Game not found")

                conn.close()
                return

        # 处理关于我们页面
        if path == '/about':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()

            content = ABOUT_TEMPLATE
            html = HTML_TEMPLATE.format(title="关于我们 - 游戏评分网站", content=content)
            self.wfile.write(html.encode('utf-8'))
            return

        # 处理成就页面
        if path == '/achievements':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()

            # 获取统计数据
            conn = sqlite3.connect('games.db')
            cursor = conn.cursor()

            cursor.execute("SELECT COUNT(*) FROM games")
            total_games = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM users")
            total_users = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM ratings")
            total_ratings = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM comments")
            total_comments = cursor.fetchone()[0]

            conn.close()

            content = ACHIEVEMENTS_TEMPLATE.format(
                total_games=total_games,
                total_users=total_users,
                total_ratings=total_ratings,
                total_comments=total_comments
            )
            html = HTML_TEMPLATE.format(title="成就 - 游戏评分网站", content=content)
            self.wfile.write(html.encode('utf-8'))
            return

        # 处理登录页面
        if path == '/login':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()

            content = LOGIN_TEMPLATE
            html = HTML_TEMPLATE.format(title="登录 - 游戏评分网站", content=content)
            self.wfile.write(html.encode('utf-8'))
            return

        # 处理注册页面
        if path == '/register':
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()

            content = REGISTER_TEMPLATE
            html = HTML_TEMPLATE.format(title="注册 - 游戏评分网站", content=content)
            self.wfile.write(html.encode('utf-8'))
            return

        # 如果没有匹配的路由，返回404
        self.send_error(404, "Page not found")

    def do_POST(self):
        # 解析URL
        parsed_path = urllib.parse.urlparse(self.path)
        path = parsed_path.path

        # 获取POST数据
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')
        form_data = urllib.parse.parse_qs(post_data)

        # 处理注册请求
        if path == '/register':
            username = form_data.get('username', [''])[0]
            password = form_data.get('password', [''])[0]

            if username and password:
                conn = sqlite3.connect('games.db')
                cursor = conn.cursor()

                # 检查用户名是否已存在
                cursor.execute("SELECT id FROM users WHERE username = ?", (username,))
                if cursor.fetchone():
                    conn.close()
                    self.send_response(302)
                    self.send_header('Location', '/register?error=username_exists')
                    self.end_headers()
                    return

                # 创建新用户
                cursor.execute(
                    "INSERT INTO users (username, password) VALUES (?, ?)",
                    (username, password)  # 实际应用中应该对密码进行哈希处理
                )
                conn.commit()
                conn.close()

                self.send_response(302)
                self.send_header('Location', '/login?success=registered')
                self.end_headers()
                return

        # 处理登录请求
        if path == '/login':
            username = form_data.get('username', [''])[0]
            password = form_data.get('password', [''])[0]

            if username and password:
                conn = sqlite3.connect('games.db')
                cursor = conn.cursor()

                # 验证用户
                cursor.execute(
                    "SELECT id FROM users WHERE username = ? AND password = ?",
                    (username, password)  # 实际应用中应该对密码进行哈希处理
                )
                user = cursor.fetchone()
                conn.close()

                if user:
                    self.send_response(302)
                    self.send_header('Location', '/')
                    self.end_headers()
                    return
                else:
                    self.send_response(302)
                    self.send_header('Location', '/login?error=invalid_credentials')
                    self.end_headers()
                    return

        # 处理评分请求
        if path.startswith('/rate/'):
            game_id = path.split('/')[-1]
            rating = form_data.get('rating', [''])[0]

            if game_id.isdigit() and rating.isdigit():
                # 在实际应用中，这里应该验证用户是否已登录
                # 简化版本中，我们假设用户ID为1
                user_id = 1

                conn = sqlite3.connect('games.db')
                cursor = conn.cursor()

                # 检查用户是否已经评分
                cursor.execute(
                    "SELECT id FROM ratings WHERE user_id = ? AND game_id = ?",
                    (user_id, game_id)
                )
                existing_rating = cursor.fetchone()

                if existing_rating:
                    # 更新评分
                    cursor.execute(
                        "UPDATE ratings SET score = ?, created_at = ? WHERE user_id = ? AND game_id = ?",
                        (rating, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), user_id, game_id)
                    )
                else:
                    # 添加新评分
                    cursor.execute(
                        "INSERT INTO ratings (score, user_id, game_id, created_at) VALUES (?, ?, ?, ?)",
                        (rating, user_id, game_id, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                    )

                conn.commit()
                conn.close()

                self.send_response(302)
                self.send_header('Location', f'/game/{game_id}')
                self.end_headers()
                return

        # 处理评论请求
        if path.startswith('/comment/'):
            game_id = path.split('/')[-1]
            content = form_data.get('content', [''])[0]

            if game_id.isdigit() and content:
                # 在实际应用中，这里应该验证用户是否已登录
                # 简化版本中，我们假设用户ID为1
                user_id = 1

                conn = sqlite3.connect('games.db')
                cursor = conn.cursor()

                # 添加评论
                cursor.execute(
                    "INSERT INTO comments (content, user_id, game_id, created_at) VALUES (?, ?, ?, ?)",
                    (content, user_id, game_id, datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                )

                conn.commit()
                conn.close()

                self.send_response(302)
                self.send_header('Location', f'/game/{game_id}')
                self.end_headers()
                return

        # 如果没有匹配的路由，返回404
        self.send_error(404, "Page not found")

# 创建并启动服务器
PORT = 9001
Handler = GameHandler
httpd = socketserver.TCPServer(("", PORT), Handler)

print(f"服务器已启动，请访问 http://localhost:{PORT}/")
httpd.serve_forever()
